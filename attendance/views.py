from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User, Group
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.utils import timezone
from django.db.models import Count, Q
from django.core.paginator import Paginator
from django.db import IntegrityError
from datetime import datetime, timedelta
import json
import logging

from studentsData.models import StudentData
from librarian.models import Librarian_param
from subLibrarian.models import Sublibrarian_param
from .models import AttendanceRecord, AttendanceSettings, AttendanceStatistics
from .utils import qr_generator, get_client_ip, get_user_agent, backdate_validator, audit_logger

logger = logging.getLogger(__name__)


# Permission checking functions
def is_scanner_only(user):
    """Check if user has scanner_only role"""
    return user.groups.filter(name='scanner_only').exists()

def is_staff_or_admin(user):
    """Check if user has staff or admin role"""
    return user.groups.filter(name__in=['librarian', 'sublibrarian', 'admin', 'staff']).exists()

def is_admin(user):
    """Check if user has admin role"""
    return user.groups.filter(name__in=['librarian', 'admin']).exists()

def has_scanner_access(user):
    """Check if user can access scanner (all roles can)"""
    return user.groups.filter(name__in=['librarian', 'sublibrarian', 'admin', 'staff', 'scanner_only']).exists()


# Scanner-only authentication views (public access)
def scanner_login(request):
    """Scanner-only login page - public access"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        if not username or not password:
            messages.error(request, 'Both username and password are required.')
            return render(request, 'attendance/scanner_login.html')

        user = authenticate(request, username=username, password=password)

        if user is not None:
            # Check if user has any attendance-related permissions
            if has_scanner_access(user):
                login(request, user)

                # Redirect based on role
                if is_scanner_only(user):
                    return redirect('attendance:scanner_page')
                else:
                    return redirect('attendance:today_attendance')
            else:
                messages.error(request, 'You do not have permission to access the scanner.')
        else:
            messages.error(request, 'Invalid username or password.')

    return render(request, 'attendance/scanner_login.html')


def scanner_logout(request):
    """Logout for scanner users"""
    logout(request)
    return redirect('attendance:scanner_login')


@login_required(login_url="/attendance/scanner-login/")
@user_passes_test(has_scanner_access, login_url="/attendance/scanner-login/")
def scanner_page(request):
    """Display the QR code scanner page"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get or create attendance settings
    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
            settings, created = AttendanceSettings.objects.get_or_create(librarian=librarian)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            settings, created = AttendanceSettings.objects.get_or_create(librarian=sublibrarian.librarian)
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        settings = None

    context = {
        'role': role,
        'settings': settings,
    }

    # Use clean template for scanner-only users, regular template for others
    if is_scanner_only(request.user):
        return render(request, 'attendance/scanner_clean.html', context)
    else:
        return render(request, 'attendance/scanner.html', context)


@login_required(login_url="/librarian/login/")
@require_http_methods(["POST"])
def scanner_result(request):
    """Process scanned QR code and return result page"""
    qr_content = request.POST.get('qr_content', '').strip()

    if not qr_content:
        context = {
            'status': 'error',
            'message': 'No QR code content received',
            'background_color': 'red'
        }
        if is_scanner_only(request.user):
            return render(request, 'attendance/scanner_result_clean.html', context)
        else:
            return render(request, 'attendance/scanner_result.html', context)

    # Validate QR code
    validation_result = qr_generator.validate_qr_content(qr_content)

    # Get user info and determine library context
    role = request.user.groups.first().name if request.user.groups.exists() else None
    librarian = None
    sublibrarian = None

    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        elif role == 'scanner_only':
            # Handle scanner-only users
            from .models import ScannerProfile
            try:
                scanner_profile = ScannerProfile.objects.get(user=request.user, is_active=True)
                librarian = scanner_profile.librarian
            except ScannerProfile.DoesNotExist:
                context = {
                    'status': 'error',
                    'message': 'Scanner account not properly configured. Please contact administrator.',
                    'background_color': 'red'
                }
                return render(request, 'attendance/scanner_result_clean.html', context)
        else:
            # Handle sublibrarian
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        context = {
            'status': 'error',
            'message': 'User not authorized for attendance scanning',
            'background_color': 'red'
        }
        if is_scanner_only(request.user):
            return render(request, 'attendance/scanner_result_clean.html', context)
        else:
            return render(request, 'attendance/scanner_result.html', context)

    # Process the result
    if validation_result['valid']:
        # Get student with optimized query using library_id from QR
        try:
            qr_library_id = validation_result.get('qr_data', {}).get('library_id')

            # Verify library_id matches current library for security
            if qr_library_id and qr_library_id != librarian.id:
                context = {
                    'status': 'error',
                    'message': 'QR code is for a different library',
                    'background_color': 'red'
                }
                if is_scanner_only(request.user):
                    return render(request, 'attendance/scanner_result_clean.html', context)
                else:
                    return render(request, 'attendance/scanner_result.html', context)

            # Optimized query: get student and verify library in single query
            student = StudentData.objects.select_related('librarian').get(
                id=validation_result['student_id'],
                librarian=librarian
            )

            # Check for existing attendance today
            today = timezone.now().date()

            # Use get_or_create to handle the unique constraint properly
            attendance, created = AttendanceRecord.objects.get_or_create(
                student=student,
                scan_date=today,
                defaults={
                    'librarian': librarian if role == 'librarian' else None,
                    'sublibrarian': sublibrarian,
                    'scanned_by': request.user,
                    'status': 'present',
                    'qr_content': qr_content,
                    'is_valid_qr': True,
                    'ip_address': get_client_ip(request),
                    'user_agent': get_user_agent(request)
                }
            )

            if created:
                # New attendance record created (first scan of the day)
                message = f'Welcome {student.name}! Sign-in recorded.'
                status = 'success'
                background_color = 'green'
            else:
                # Existing record found - handle toggle logic

                # Update scan info
                attendance.scanned_by = request.user
                attendance.qr_content = qr_content
                attendance.ip_address = get_client_ip(request)
                attendance.user_agent = get_user_agent(request)

                # Handle sign-in/sign-out toggle logic
                if attendance.checkout_datetime:
                    # Student was already checked out, this is a re-entry (sign-in)
                    attendance.checkout_datetime = None
                    attendance.checkout_by = None
                    attendance.logout_reason = None
                    attendance.status = 'present'
                    attendance.is_manual_checkout = False
                    attendance.is_backdated = False
                    attendance.save()

                    message = f'Welcome back {student.name}! Sign-in recorded.'
                    status = 'success'
                    background_color = 'green'
                else:
                    # Student is currently present, this is a checkout scan (sign-out)
                    attendance.checkout_datetime = timezone.now()
                    attendance.checkout_by = request.user
                    attendance.status = 'checked_out'
                    attendance.save()

                    message = f'Goodbye {student.name}! Sign-out recorded.'
                    status = 'success'
                    background_color = 'blue'

            context = {
                'status': status,
                'message': message,
                'student': student,
                'attendance': attendance,
                'background_color': background_color
            }
            if is_scanner_only(request.user):
                return render(request, 'attendance/scanner_result_clean.html', context)
            else:
                return render(request, 'attendance/scanner_result.html', context)

        except StudentData.DoesNotExist:
            context = {
                'status': 'error',
                'message': 'Student not found',
                'background_color': 'red'
            }
            if is_scanner_only(request.user):
                return render(request, 'attendance/scanner_result_clean.html', context)
            else:
                return render(request, 'attendance/scanner_result.html', context)

    else:
        # Handle invalid/expired QR codes
        student = None
        if validation_result.get('student_id'):
            try:
                student = StudentData.objects.get(id=validation_result['student_id'])
            except StudentData.DoesNotExist:
                pass

        # Create or update attendance record for tracking (handle duplicates gracefully)
        today = timezone.now().date()
        attendance_defaults = {
            'librarian': librarian if role == 'librarian' else None,
            'sublibrarian': sublibrarian,
            'scanned_by': request.user,
            'status': validation_result['status'],
            'qr_content': qr_content,
            'is_valid_qr': False,
            'ip_address': get_client_ip(request),
            'user_agent': get_user_agent(request),
            'notes': validation_result['message']
        }

        if student:
            # For known students, use get_or_create to handle duplicates
            attendance, created = AttendanceRecord.objects.get_or_create(
                student=student,
                scan_date=today,
                defaults=attendance_defaults
            )
            if not created:
                # Update existing record with latest scan info
                attendance.scanned_by = request.user
                attendance.qr_content = qr_content
                attendance.ip_address = get_client_ip(request)
                attendance.user_agent = get_user_agent(request)
                attendance.notes = f"{attendance.notes or ''}\n{validation_result['message']}".strip()
                attendance.save()
        else:
            # For unknown students, create without unique constraint issues
            AttendanceRecord.objects.create(
                student=None,
                scan_date=today,
                **attendance_defaults
            )

        background_color = 'red' if validation_result['status'] == 'invalid' else 'orange'

        context = {
            'status': validation_result['status'],
            'message': validation_result['message'],
            'student': student,
            'background_color': background_color
        }
        if is_scanner_only(request.user):
            return render(request, 'attendance/scanner_result_clean.html', context)
        else:
            return render(request, 'attendance/scanner_result.html', context)


@login_required(login_url="/librarian/login/")
@user_passes_test(lambda u: is_staff_or_admin(u) or is_scanner_only(u), login_url="/librarian/login/")
def today_attendance(request):
    """Display today's attendance summary"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        messages.error(request, "User not authorized for attendance viewing")
        return redirect('/')

    today = timezone.now().date()

    # Get today's attendance records
    today_records = AttendanceRecord.objects.filter(
        scan_date=today
    ).select_related('student')

    if role == 'librarian':
        today_records = today_records.filter(librarian=librarian)
    else:
        today_records = today_records.filter(sublibrarian__librarian=librarian)

    # Group by status
    present_students = today_records.filter(status='present')
    expired_students = today_records.filter(status='expired')
    invalid_students = today_records.filter(status='invalid')
    checked_out_students = today_records.filter(status='checked_out')

    # Get live attendance (currently present students - signed in but not checked out)
    live_students = today_records.filter(
        status='present',
        checkout_datetime__isnull=True
    ).select_related('student')

    # Get all students who have attended today (including those who checked out)
    total_attended_today = today_records.filter(
        status__in=['present', 'checked_out']
    ).distinct().count()

    # Get statistics
    stats = {
        'total_present': present_students.count(),
        'total_expired': expired_students.count(),
        'total_invalid': invalid_students.count(),
        'total_checked_out': checked_out_students.count(),
        'total_attended': total_attended_today,
        'total_scans': today_records.count(),
        'live_count': live_students.count(),
    }

    context = {
        'role': role,
        'today': today,
        'stats': stats,
        'present_students': present_students,
        'expired_students': expired_students,
        'invalid_students': invalid_students,
        'checked_out_students': checked_out_students,
        'live_students': live_students,
    }

    return render(request, 'attendance/today.html', context)


@login_required(login_url="/librarian/login/")
@user_passes_test(is_staff_or_admin, login_url="/librarian/login/")
def live_students_list(request):
    """API endpoint to get list of currently present students"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    today = timezone.now().date()

    # Get currently present students
    live_records = AttendanceRecord.objects.filter(
        scan_date=today,
        status='present',
        checkout_datetime__isnull=True
    ).select_related('student')

    if role == 'librarian':
        live_records = live_records.filter(librarian=librarian)
    else:
        live_records = live_records.filter(sublibrarian__librarian=librarian)

    # Format data
    students_data = []
    for record in live_records:
        # Calculate duration
        duration = timezone.now() - record.scan_datetime
        hours, remainder = divmod(duration.total_seconds(), 3600)
        minutes, _ = divmod(remainder, 60)

        students_data.append({
            'id': record.id,
            'student_id': record.student.id,
            'student_name': record.student.name,
            'student_unique_id': record.student.unique_id,
            'check_in_time': record.scan_datetime.strftime('%H:%M'),
            'duration': f"{int(hours)}h {int(minutes)}m",
            'duration_minutes': int(duration.total_seconds() / 60),
        })

    return JsonResponse({
        'students': students_data,
        'count': len(students_data)
    })


@login_required(login_url="/attendance/scanner-login/")
@user_passes_test(is_staff_or_admin, login_url="/attendance/scanner-login/")
@require_http_methods(["POST"])
def checkout_student(request):
    """Checkout a student immediately"""
    try:
        data = json.loads(request.body)
        attendance_id = data.get('attendance_id')
        reason = data.get('reason', 'manual_correction')

        if not attendance_id:
            return JsonResponse({'error': 'Attendance ID required'}, status=400)

        # Get the attendance record
        attendance = get_object_or_404(AttendanceRecord, id=attendance_id)

        # Verify the record belongs to current user's library
        role = request.user.groups.first().name if request.user.groups.exists() else None
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
            if attendance.librarian != librarian:
                return JsonResponse({'error': 'Unauthorized'}, status=403)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            if attendance.sublibrarian != sublibrarian:
                return JsonResponse({'error': 'Unauthorized'}, status=403)

        # Check if already checked out
        if attendance.checkout_datetime:
            return JsonResponse({'error': 'Student already checked out'}, status=400)

        # Perform checkout
        attendance.checkout_datetime = timezone.now()
        attendance.checkout_by = request.user
        attendance.logout_reason = reason
        attendance.is_manual_checkout = True
        attendance.status = 'checked_out'
        attendance.save()

        # Log the action
        audit_logger.log_manual_checkout(
            user=request.user,
            attendance_record=attendance,
            reason=reason,
            is_backdated=False,
            request=request
        )

        return JsonResponse({
            'success': True,
            'message': f'Successfully checked out {attendance.student.name}',
            'checkout_time': attendance.checkout_datetime.strftime('%H:%M:%S')
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Checkout error: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required(login_url="/attendance/scanner-login/")
@user_passes_test(is_staff_or_admin, login_url="/attendance/scanner-login/")
@require_http_methods(["POST"])
def backdate_checkout(request):
    """Checkout a student with backdated time"""
    try:
        data = json.loads(request.body)
        attendance_id = data.get('attendance_id')
        checkout_time = data.get('checkout_time')  # Format: "HH:MM"
        reason = data.get('reason')
        notes = data.get('notes', '')

        if not all([attendance_id, checkout_time, reason]):
            return JsonResponse({'error': 'Attendance ID, checkout time, and reason are required'}, status=400)

        # Get the attendance record
        attendance = get_object_or_404(AttendanceRecord, id=attendance_id)

        # Verify the record belongs to current user's library
        role = request.user.groups.first().name if request.user.groups.exists() else None
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
            if attendance.librarian != librarian:
                return JsonResponse({'error': 'Unauthorized'}, status=403)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            if attendance.sublibrarian != sublibrarian:
                return JsonResponse({'error': 'Unauthorized'}, status=403)

        # Check if already checked out
        if attendance.checkout_datetime:
            return JsonResponse({'error': 'Student already checked out'}, status=400)

        # Validate backdating rules using utility
        validation_result = backdate_validator.validate_backdate_time(attendance, checkout_time)

        if not validation_result['valid']:
            return JsonResponse({'error': validation_result['error']}, status=400)

        checkout_datetime = validation_result['checkout_datetime']

        # Perform backdated checkout
        attendance.checkout_datetime = checkout_datetime
        attendance.checkout_by = request.user
        attendance.logout_reason = reason
        attendance.is_manual_checkout = True
        attendance.is_backdated = True
        attendance.status = 'checked_out'
        if notes:
            attendance.notes = f"{attendance.notes or ''}\nBackdated checkout: {notes}".strip()
        attendance.save()

        # Log the action
        audit_logger.log_manual_checkout(
            user=request.user,
            attendance_record=attendance,
            reason=reason,
            is_backdated=True,
            request=request
        )

        return JsonResponse({
            'success': True,
            'message': f'Successfully backdated checkout for {attendance.student.name}',
            'checkout_time': attendance.checkout_datetime.strftime('%H:%M:%S')
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Backdate checkout error: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required(login_url="/librarian/login/")
def manage_scanner_accounts(request):
    """Admin page to manage scanner-only accounts"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get current librarian - try to find librarian profile
    try:
        librarian = Librarian_param.objects.get(user=request.user)
    except Librarian_param.DoesNotExist:
        # Try sublibrarian
        try:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
        except Sublibrarian_param.DoesNotExist:
            messages.error(request, 'No librarian profile found. Please contact administrator.')
            return redirect('/')

    # Get existing scanner accounts for this library
    try:
        scanner_group = Group.objects.get(name='scanner_only')
        scanner_users = User.objects.filter(groups=scanner_group).select_related('scanner_profile__librarian').order_by('username')
    except Group.DoesNotExist:
        messages.error(request, 'Scanner group not found. Please run: python manage.py setup_attendance_groups')
        scanner_users = User.objects.none()

    # Filter to show only accounts created by this librarian (optional)
    # For now, show all scanner accounts

    context = {
        'role': role,
        'librarian': librarian,
        'scanner_users': scanner_users,
    }

    return render(request, 'attendance/manage_scanner_accounts.html', context)


@login_required(login_url="/librarian/login/")
@require_http_methods(["POST"])
def create_scanner_account(request):
    """Create a new scanner-only account"""
    try:
        data = json.loads(request.body)
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        full_name = data.get('full_name', '').strip()

        if not all([username, password, full_name]):
            return JsonResponse({'error': 'Username, password, and full name are required'}, status=400)

        # Validate username format
        import re
        if not re.match(r'^[a-zA-Z0-9._-]+$', username):
            return JsonResponse({'error': 'Username can only contain letters, numbers, periods, hyphens, and underscores'}, status=400)

        # Check if username already exists
        if User.objects.filter(username=username).exists():
            return JsonResponse({'error': 'Username already exists'}, status=400)

        # Create user
        user = User.objects.create_user(
            username=username,
            password=password,
            first_name=full_name.split()[0] if full_name.split() else full_name,
            last_name=' '.join(full_name.split()[1:]) if len(full_name.split()) > 1 else '',
            email=f"{username}@scanner.local"  # Dummy email
        )

        # Add to scanner_only group
        scanner_group, created = Group.objects.get_or_create(name='scanner_only')
        user.groups.add(scanner_group)

        # Get current librarian to associate scanner with
        try:
            librarian = Librarian_param.objects.get(user=request.user)
        except Librarian_param.DoesNotExist:
            # Try sublibrarian
            try:
                sublibrarian = Sublibrarian_param.objects.get(user=request.user)
                librarian = sublibrarian.librarian
            except Sublibrarian_param.DoesNotExist:
                return JsonResponse({'error': 'No librarian profile found. Cannot create scanner account.'}, status=400)

        # Create scanner profile to link user to library
        from .models import ScannerProfile
        ScannerProfile.objects.create(
            user=user,
            librarian=librarian,
            created_by=request.user
        )

        # Log the creation
        audit_logger.log_settings_change(
            user=request.user,
            setting_name='scanner_account_created',
            old_value=None,
            new_value=f"Created scanner account: {username} for library: {librarian.library_name}",
            request=request
        )

        return JsonResponse({
            'success': True,
            'message': f'Scanner account "{username}" created successfully',
            'user_id': user.id,
            'username': user.username,
            'full_name': user.get_full_name() or user.username
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error creating scanner account: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required(login_url="/librarian/login/")
@require_http_methods(["POST"])
def delete_scanner_account(request):
    """Delete a scanner-only account"""
    try:
        data = json.loads(request.body)
        user_id = data.get('user_id')

        if not user_id:
            return JsonResponse({'error': 'User ID required'}, status=400)

        # Get the user
        user = get_object_or_404(User, id=user_id)

        # Verify it's a scanner-only account
        if not user.groups.filter(name='scanner_only').exists():
            return JsonResponse({'error': 'Can only delete scanner-only accounts'}, status=400)

        username = user.username
        user.delete()

        # Log the deletion
        audit_logger.log_settings_change(
            user=request.user,
            setting_name='scanner_account_deleted',
            old_value=f"Scanner account: {username}",
            new_value=None,
            request=request
        )

        return JsonResponse({
            'success': True,
            'message': f'Scanner account "{username}" deleted successfully'
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error deleting scanner account: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required(login_url="/librarian/login/")
def today_students_list(request, status):
    """API endpoint to get students list for a specific status"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    today = timezone.now().date()

    # Get records for the status
    records = AttendanceRecord.objects.filter(
        scan_date=today,
        status=status
    ).select_related('student')

    if role == 'librarian':
        records = records.filter(librarian=librarian)
    else:
        records = records.filter(sublibrarian__librarian=librarian)

    # Format data
    students_data = []
    for record in records:
        student_data = {
            'name': record.student.name,
            'unique_id': record.student.unique_id,
            'email': record.student.email,
            'scan_time': record.scan_datetime.strftime('%H:%M:%S'),
            'status': record.status
        }

        # Add checkout information for checked out students
        if status == 'checked_out' and record.checkout_datetime:
            student_data['checkout_time'] = record.checkout_datetime.strftime('%H:%M:%S')

        students_data.append(student_data)

    return JsonResponse({
        'students': students_data,
        'count': len(students_data)
    })


@login_required(login_url="/librarian/login/")
def attendance_log(request):
    """Display attendance logs with filtering and statistics"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        messages.error(request, "User not authorized for attendance viewing")
        return redirect('/')

    # Get date range from request
    date_range = request.GET.get('range', '30')  # Default 30 days
    end_date = timezone.now().date()

    if date_range == '30':
        start_date = end_date - timedelta(days=30)
    elif date_range == '90':
        start_date = end_date - timedelta(days=90)
    elif date_range == '180':
        start_date = end_date - timedelta(days=180)
    elif date_range == '365':
        start_date = end_date - timedelta(days=365)
    elif date_range == 'custom':
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else end_date - timedelta(days=30)
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date() if end_date_str else end_date
        except ValueError:
            start_date = end_date - timedelta(days=30)
    else:
        start_date = end_date - timedelta(days=30)

    # Get attendance records
    records = AttendanceRecord.objects.filter(
        scan_date__range=[start_date, end_date]
    ).select_related('student').order_by('-scan_datetime')

    if role == 'librarian':
        records = records.filter(librarian=librarian)
    else:
        records = records.filter(sublibrarian__librarian=librarian)

    # Pagination
    paginator = Paginator(records, 50)  # 50 records per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate statistics
    total_records = records.count()
    present_count = records.filter(status='present').count()
    expired_count = records.filter(status='expired').count()
    invalid_count = records.filter(status='invalid').count()
    unique_students = records.values('student').distinct().count()

    # Most frequent visitor
    most_frequent = records.filter(status='present').values('student__name', 'student__unique_id').annotate(
        visit_count=Count('id')
    ).order_by('-visit_count').first()

    # Day with max attendance
    max_attendance_day = records.filter(status='present').values('scan_date').annotate(
        daily_count=Count('id')
    ).order_by('-daily_count').first()

    # Students with multiple visits (potential overuse)
    overused_students = records.filter(status='present').values(
        'student__name', 'student__unique_id'
    ).annotate(
        visit_count=Count('id')
    ).filter(visit_count__gt=20).order_by('-visit_count')  # More than 20 visits in period

    context = {
        'role': role,
        'page_obj': page_obj,
        'start_date': start_date,
        'end_date': end_date,
        'date_range': date_range,
        'stats': {
            'total_records': total_records,
            'present_count': present_count,
            'expired_count': expired_count,
            'invalid_count': invalid_count,
            'unique_students': unique_students,
            'most_frequent': most_frequent,
            'max_attendance_day': max_attendance_day,
            'overused_students': overused_students,
        }
    }

    return render(request, 'attendance/log.html', context)


@login_required(login_url="/librarian/login/")
def export_attendance(request):
    """Export attendance data to Excel"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    # Get librarian
    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    # Get date range from request
    date_range = request.GET.get('range', '30')
    end_date = timezone.now().date()

    if date_range == '30':
        start_date = end_date - timedelta(days=30)
    elif date_range == '90':
        start_date = end_date - timedelta(days=90)
    elif date_range == '180':
        start_date = end_date - timedelta(days=180)
    elif date_range == '365':
        start_date = end_date - timedelta(days=365)
    elif date_range == 'custom':
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date() if start_date_str else end_date - timedelta(days=30)
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date() if end_date_str else end_date
        except ValueError:
            start_date = end_date - timedelta(days=30)
    else:
        start_date = end_date - timedelta(days=30)

    # Get attendance records
    records = AttendanceRecord.objects.filter(
        scan_date__range=[start_date, end_date]
    ).select_related('student').order_by('-scan_datetime')

    if role == 'librarian':
        records = records.filter(librarian=librarian)
    else:
        records = records.filter(sublibrarian__librarian=librarian)

    # Create Excel response using openpyxl
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        from openpyxl.utils import get_column_letter

        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Attendance Report"

        # Headers
        headers = [
            'Date', 'Time', 'Student Name', 'Student ID', 'Email', 'Status',
            'Scanned By', 'IP Address', 'Notes'
        ]

        # Style headers
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")

        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment

        # Add data
        for row, record in enumerate(records, 2):
            ws.cell(row=row, column=1, value=record.scan_date.strftime('%Y-%m-%d'))
            ws.cell(row=row, column=2, value=record.scan_datetime.strftime('%H:%M:%S'))
            ws.cell(row=row, column=3, value=record.student.name if record.student else 'Unknown')
            ws.cell(row=row, column=4, value=record.student.unique_id if record.student else 'N/A')
            ws.cell(row=row, column=5, value=record.student.email if record.student else 'N/A')
            ws.cell(row=row, column=6, value=record.status.title())
            ws.cell(row=row, column=7, value=record.scanned_by.username if record.scanned_by else 'System')
            ws.cell(row=row, column=8, value=record.ip_address or 'N/A')
            ws.cell(row=row, column=9, value=record.notes or '')

        # Auto-adjust column widths
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].auto_size = True

        # Create response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="attendance_{start_date}_to_{end_date}.xlsx"'

        wb.save(response)

    except ImportError:
        # Fallback to CSV if openpyxl is not available
        import csv
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="attendance_{start_date}_to_{end_date}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Date', 'Time', 'Student Name', 'Student ID', 'Email', 'Status',
            'Scanned By', 'IP Address', 'Notes'
        ])

        for record in records:
            writer.writerow([
                record.scan_date.strftime('%Y-%m-%d'),
                record.scan_datetime.strftime('%H:%M:%S'),
                record.student.name if record.student else 'Unknown',
                record.student.unique_id if record.student else 'N/A',
                record.student.email if record.student else 'N/A',
                record.status.title(),
                record.scanned_by.username if record.scanned_by else 'System',
                record.ip_address or 'N/A',
                record.notes or ''
            ])

    return response


@login_required(login_url="/librarian/login/")
def debug_attendance(request):
    """Debug endpoint to check attendance records"""
    if not request.user.is_superuser:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    from django.utils import timezone
    import datetime

    today = timezone.now().date()
    yesterday = today - datetime.timedelta(days=1)

    # Get recent records
    recent_records = AttendanceRecord.objects.filter(
        scan_date__gte=yesterday
    ).select_related('student').order_by('-scan_datetime')[:20]

    records_data = []
    for record in recent_records:
        records_data.append({
            'id': record.id,
            'student_name': record.student.name if record.student else 'Unknown',
            'student_id': record.student.id if record.student else None,
            'status': record.status,
            'scan_date': record.scan_date.isoformat(),
            'scan_datetime': record.scan_datetime.isoformat(),
            'checkout_datetime': record.checkout_datetime.isoformat() if record.checkout_datetime else None,
            'scanned_by': record.scanned_by.username if record.scanned_by else None,
            'is_valid_qr': record.is_valid_qr,
        })

    return JsonResponse({
        'total_records': AttendanceRecord.objects.count(),
        'today_records': AttendanceRecord.objects.filter(scan_date=today).count(),
        'recent_records': records_data,
        'today': today.isoformat(),
    })


@login_required(login_url="/librarian/login/")
@csrf_exempt
def api_scan_qr(request):
    """API endpoint for QR code scanning"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        data = json.loads(request.body)
        qr_content = data.get('qr_content', '').strip()

        if not qr_content:
            return JsonResponse({'error': 'No QR content provided'}, status=400)

        # Validate QR code
        validation_result = qr_generator.validate_qr_content(qr_content)

        return JsonResponse({
            'valid': validation_result['valid'],
            'status': validation_result['status'],
            'message': validation_result['message'],
            'student_id': validation_result.get('student_id')
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"API scan error: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@login_required(login_url="/librarian/login/")
def api_today_stats(request):
    """API endpoint for today's attendance statistics with caching"""
    from django.core.cache import cache

    role = request.user.groups.first().name if request.user.groups.exists() else None

    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    today = timezone.now().date()
    cache_key = f"attendance_stats_{librarian.id}_{today}"

    # Try to get from cache first (5 minute cache)
    cached_stats = cache.get(cache_key)
    if cached_stats:
        return JsonResponse(cached_stats)

    # Get today's statistics
    stats = AttendanceStatistics.objects.filter(
        librarian=librarian,
        date=today
    ).first()

    # Get live count
    live_count = AttendanceRecord.objects.filter(
        scan_date=today,
        status='present',
        checkout_datetime__isnull=True
    ).count()

    if role == 'admin':
        live_count = AttendanceRecord.objects.filter(
            scan_date=today,
            status='present',
            checkout_datetime__isnull=True,
            librarian=librarian
        ).count()
    else:
        live_count = AttendanceRecord.objects.filter(
            scan_date=today,
            status='present',
            checkout_datetime__isnull=True,
            sublibrarian__librarian=librarian
        ).count()

    if stats:
        response_data = {
            'date': today.isoformat(),
            'total_present': stats.total_present,
            'total_expired': stats.total_expired,
            'total_invalid': stats.total_invalid,
            'unique_students': stats.unique_students,
            'total_scans': stats.total_scans,
            'live_count': live_count
        }
    else:
        response_data = {
            'date': today.isoformat(),
            'total_present': 0,
            'total_expired': 0,
            'total_invalid': 0,
            'unique_students': 0,
            'total_scans': 0,
            'live_count': live_count
        }

    # Cache the response for 5 minutes
    cache.set(cache_key, response_data, 300)
    return JsonResponse(response_data)


@login_required(login_url="/librarian/login/")
def attendance_settings(request):
    """Attendance settings page"""
    role = request.user.groups.first().name if request.user.groups.exists() else None

    try:
        if role == 'librarian':
            librarian = Librarian_param.objects.get(user=request.user)
        else:
            sublibrarian = Sublibrarian_param.objects.get(user=request.user)
            librarian = sublibrarian.librarian
    except (Librarian_param.DoesNotExist, Sublibrarian_param.DoesNotExist):
        messages.error(request, "User not authorized")
        return redirect('/')

    settings, created = AttendanceSettings.objects.get_or_create(librarian=librarian)

    if request.method == 'POST':
        # Update settings
        settings.qr_expiry_days = int(request.POST.get('qr_expiry_days', 30))
        settings.allow_multiple_scans_per_day = request.POST.get('allow_multiple_scans_per_day') == 'on'
        settings.success_sound_enabled = request.POST.get('success_sound_enabled') == 'on'
        settings.error_sound_enabled = request.POST.get('error_sound_enabled') == 'on'
        settings.notify_on_expired_scan = request.POST.get('notify_on_expired_scan') == 'on'
        settings.notify_on_invalid_qr = request.POST.get('notify_on_invalid_qr') == 'on'
        settings.save()

        messages.success(request, 'Attendance settings updated successfully!')
        return redirect('attendance:attendance_settings')

    context = {
        'role': role,
        'settings': settings,
    }

    return render(request, 'attendance/settings.html', context)


@login_required(login_url="/librarian/login/")
def debug_user_groups(request):
    """Debug view to check user groups"""
    user_groups = list(request.user.groups.values_list('name', flat=True))
    return JsonResponse({
        'username': request.user.username,
        'groups': user_groups,
        'is_staff': request.user.is_staff,
        'is_superuser': request.user.is_superuser
    })
