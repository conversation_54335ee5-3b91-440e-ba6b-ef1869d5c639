from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from attendance.models import AttendanceRecord
from studentsData.models import StudentData
from librarian.models import Librarian_param


class Command(BaseCommand):
    help = 'Test duplicate scan handling to verify integrity constraint fixes'

    def handle(self, *args, **options):
        self.stdout.write("Testing duplicate scan handling...")
        
        try:
            # Get a test student and scanner user
            student = StudentData.objects.first()
            scanner_user = User.objects.filter(groups__name='scanner_only').first()
            
            if not student:
                self.stdout.write(self.style.ERROR("No students found for testing"))
                return
                
            if not scanner_user:
                self.stdout.write(self.style.ERROR("No scanner users found for testing"))
                return
                
            librarian = student.librarian
            today = timezone.now().date()
            
            self.stdout.write(f"Testing with student: {student.name}")
            self.stdout.write(f"Testing with scanner: {scanner_user.username}")
            
            # Test 1: Create first attendance record
            self.stdout.write("\n1. Creating first attendance record...")
            attendance1, created1 = AttendanceRecord.objects.get_or_create(
                student=student,
                scan_date=today,
                defaults={
                    'librarian': librarian,
                    'scanned_by': scanner_user,
                    'status': 'present',
                    'qr_content': 'test_qr_1',
                    'is_valid_qr': True,
                }
            )
            
            if created1:
                self.stdout.write(self.style.SUCCESS("✅ First record created successfully"))
            else:
                self.stdout.write(self.style.WARNING("⚠️ Record already existed"))
            
            # Test 2: Try to create duplicate (should handle gracefully)
            self.stdout.write("\n2. Testing duplicate scan handling...")
            attendance2, created2 = AttendanceRecord.objects.get_or_create(
                student=student,
                scan_date=today,
                defaults={
                    'librarian': librarian,
                    'scanned_by': scanner_user,
                    'status': 'present',
                    'qr_content': 'test_qr_2',
                    'is_valid_qr': True,
                }
            )
            
            if not created2:
                self.stdout.write(self.style.SUCCESS("✅ Duplicate handled gracefully - no new record created"))
                self.stdout.write(f"   Original QR: {attendance2.qr_content}")
            else:
                self.stdout.write(self.style.ERROR("❌ Unexpected: New record was created"))
            
            # Test 3: Update existing record
            self.stdout.write("\n3. Testing record update...")
            attendance2.qr_content = 'test_qr_updated'
            attendance2.scanned_by = scanner_user
            attendance2.save()
            self.stdout.write(self.style.SUCCESS("✅ Record updated successfully"))
            
            # Test 4: Test invalid QR with same student
            self.stdout.write("\n4. Testing invalid QR handling...")
            try:
                # This should not create a duplicate due to our fixes
                attendance3, created3 = AttendanceRecord.objects.get_or_create(
                    student=student,
                    scan_date=today,
                    defaults={
                        'librarian': librarian,
                        'scanned_by': scanner_user,
                        'status': 'expired',
                        'qr_content': 'test_qr_expired',
                        'is_valid_qr': False,
                    }
                )
                
                if not created3:
                    self.stdout.write(self.style.SUCCESS("✅ Invalid QR handled gracefully - updated existing record"))
                else:
                    self.stdout.write(self.style.ERROR("❌ Unexpected: New record created for invalid QR"))
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ Error with invalid QR: {e}"))
            
            # Summary
            self.stdout.write("\n" + "="*50)
            self.stdout.write("SUMMARY:")
            self.stdout.write(f"Student: {student.name}")
            self.stdout.write(f"Date: {today}")
            
            final_records = AttendanceRecord.objects.filter(
                student=student,
                scan_date=today
            )
            
            self.stdout.write(f"Total records for today: {final_records.count()}")
            
            if final_records.count() == 1:
                self.stdout.write(self.style.SUCCESS("✅ SUCCESS: Only one record exists (as expected)"))
                record = final_records.first()
                self.stdout.write(f"   Status: {record.status}")
                self.stdout.write(f"   QR Content: {record.qr_content}")
                self.stdout.write(f"   Scanned by: {record.scanned_by.username}")
            else:
                self.stdout.write(self.style.ERROR(f"❌ ISSUE: {final_records.count()} records found"))
                for i, record in enumerate(final_records, 1):
                    self.stdout.write(f"   Record {i}: {record.status} - {record.qr_content}")
            
            self.stdout.write("\n✅ Test completed!")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Test failed with error: {e}"))
            import traceback
            traceback.print_exc()
