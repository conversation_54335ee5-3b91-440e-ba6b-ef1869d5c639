from django.db import models
from django.utils import timezone
from studentsData.models import StudentData
from librarian.models import Librarian_param
from subLibrarian.models import Sublibrarian_param
from django.contrib.auth.models import User


class AttendanceRecord(models.Model):
    """Model to track student attendance via QR code scanning"""

    ATTENDANCE_STATUS_CHOICES = [
        ('present', 'Present'),
        ('expired', 'Expired'),
        ('invalid', 'Invalid'),
        ('checked_out', 'Checked Out'),
    ]

    LOGOUT_REASON_CHOICES = [
        ('forgot_scan', 'Forgot to Scan Out'),
        ('left_early', 'Left Early (Emergency)'),
        ('system_error', 'System/Scanner Error'),
        ('manual_correction', 'Manual Correction'),
    ]

    student = models.ForeignKey(StudentData, on_delete=models.CASCADE, related_name='attendance_records')
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE, null=True, blank=True)
    sublibrarian = models.<PERSON><PERSON>ey(Sublibrarian_param, on_delete=models.CASCADE, null=True, blank=True)
    scanned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scanned_attendances')

    # Attendance details
    scan_datetime = models.DateTimeField(default=timezone.now)
    scan_date = models.DateField(default=timezone.now)
    status = models.CharField(max_length=20, choices=ATTENDANCE_STATUS_CHOICES, default='present')

    # QR Code validation details
    qr_content = models.TextField(help_text="Original QR code content that was scanned")
    is_valid_qr = models.BooleanField(default=True)

    # Check-out related fields
    checkout_datetime = models.DateTimeField(null=True, blank=True, help_text="When student checked out")
    checkout_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='checkout_attendances')
    logout_reason = models.CharField(max_length=20, choices=LOGOUT_REASON_CHOICES, null=True, blank=True)
    is_manual_checkout = models.BooleanField(default=False, help_text="Was this a manual/forced checkout")
    is_backdated = models.BooleanField(default=False, help_text="Was the checkout time backdated")

    # Additional metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    notes = models.TextField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-scan_datetime']
        indexes = [
            models.Index(fields=['scan_date']),
            models.Index(fields=['student', 'scan_date']),
            models.Index(fields=['status']),
        ]
        # Prevent duplicate attendance for same student on same day
        unique_together = ['student', 'scan_date']

    def __str__(self):
        return f"{self.student.name} - {self.scan_date} ({self.status})"

    @property
    def is_currently_present(self):
        """Check if student is currently present (checked in but not checked out)"""
        return self.status == 'present' and self.checkout_datetime is None

    @property
    def duration_minutes(self):
        """Calculate duration in minutes if checked out"""
        if self.checkout_datetime:
            delta = self.checkout_datetime - self.scan_datetime
            return int(delta.total_seconds() / 60)
        return None

    @property
    def is_today(self):
        """Check if attendance is for today"""
        return self.scan_date == timezone.now().date()

    @property
    def scanner_name(self):
        """Get the name of person who scanned"""
        if self.librarian:
            return self.librarian.library_name
        elif self.sublibrarian:
            return f"{self.sublibrarian.librarian.library_name} (Sub)"
        return self.scanned_by.username


class AttendanceSettings(models.Model):
    """Settings for attendance module per library"""

    librarian = models.OneToOneField(Librarian_param, on_delete=models.CASCADE, related_name='attendance_settings')

    # QR Code settings
    qr_expiry_days = models.IntegerField(default=30, help_text="Days after which QR code expires")
    allow_multiple_scans_per_day = models.BooleanField(default=False, help_text="Allow multiple attendance records per day")

    # Audio/Visual feedback settings
    success_sound_enabled = models.BooleanField(default=True)
    error_sound_enabled = models.BooleanField(default=True)

    # Notification settings
    notify_on_expired_scan = models.BooleanField(default=True)
    notify_on_invalid_qr = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Attendance Settings - {self.librarian.library_name}"


class AttendanceStatistics(models.Model):
    """Daily attendance statistics for quick reporting"""

    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE, related_name='attendance_stats')
    date = models.DateField()

    # Daily counts
    total_present = models.IntegerField(default=0)
    total_expired = models.IntegerField(default=0)
    total_invalid = models.IntegerField(default=0)
    unique_students = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['librarian', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.librarian.library_name} - {self.date} ({self.total_present} present)"


class AttendanceAuditLog(models.Model):
    """Audit log for attendance system actions"""

    ACTION_CHOICES = [
        ('manual_checkout', 'Manual Checkout'),
        ('backdated_checkout', 'Backdated Checkout'),
        ('forced_checkout', 'Forced Checkout'),
        ('attendance_edit', 'Attendance Edit'),
        ('attendance_delete', 'Attendance Delete'),
        ('settings_change', 'Settings Change'),
    ]

    # Core fields
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='audit_actions')
    performed_at = models.DateTimeField(auto_now_add=True)

    # Related records
    attendance_record = models.ForeignKey(AttendanceRecord, on_delete=models.CASCADE, null=True, blank=True)
    student = models.ForeignKey(StudentData, on_delete=models.CASCADE, null=True, blank=True)
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE, null=True, blank=True)

    # Action details
    reason = models.CharField(max_length=20, null=True, blank=True)
    original_data = models.JSONField(null=True, blank=True, help_text="Original data before change")
    new_data = models.JSONField(null=True, blank=True, help_text="New data after change")
    notes = models.TextField(null=True, blank=True)

    # Metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ['-performed_at']
        indexes = [
            models.Index(fields=['action', 'performed_at']),
            models.Index(fields=['student', 'performed_at']),
            models.Index(fields=['librarian', 'performed_at']),
        ]

    def __str__(self):
        return f"{self.get_action_display()} by {self.performed_by.username} at {self.performed_at}"

    @property
    def total_scans(self):
        """Total number of scans for the day"""
        return self.total_present + self.total_expired + self.total_invalid


class ScannerProfile(models.Model):
    """Profile to link scanner-only users to specific libraries"""

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='scanner_profile')
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE, related_name='scanner_users')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_scanners')
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['user', 'librarian']
        indexes = [
            models.Index(fields=['librarian', 'is_active']),
        ]

    def __str__(self):
        return f"Scanner {self.user.username} for {self.librarian.library_name}"
