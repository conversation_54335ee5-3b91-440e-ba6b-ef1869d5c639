
        // Firebase configuration - using environment variables
        const firebaseConfig = {
            apiKey: "AIzaSyDw-BI4MFB1PKezXYPIZyee8IizHw2QDjI",
            authDomain: "librainian-app.firebaseapp.com",
            projectId: "librainian-app",
            storageBucket: "librainian-app.firebasestorage.app",
            messagingSenderId: "623132670328",
            appId: "1:623132670328:web:982c79e13e3cc69f3df08d",
            measurementId: "G-XNDKJL6JWH"
        };

        // VAPID Key for Web Push
        const vapidKey = "BFm8KEWYXyt703OsjQ4338IbyV72W3m6nndMoZhzRV9SlSj0UHMv4INixoql0AJLWh6LJKC1CrP3r_M8YqsGrAY";

       

        // Initialize Firebase
        const app = firebase.initializeApp(firebaseConfig);
        const messaging = firebase.messaging();

        // Enhanced notification system with soft permission requests
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔔 Enhanced push notifications script loaded');
            console.log('🔧 Firebase config:', firebaseConfig);
            console.log('🔑 VAPID key:', vapidKey);

            if ('Notification' in window) {
                console.log('✅ Browser supports notifications');

                // Check current permission status
                const currentPermission = Notification.permission;
                console.log('📊 Current permission status:', currentPermission);

                if (currentPermission === 'granted') {
                    // Permission already granted, register token
                    registerDeviceToken();
                } else if (currentPermission === 'default') {
                    // Show soft permission request after user interaction
                    setupSoftPermissionRequest();
                } else {
                    // Permission denied, show help message
                    console.log('❌ Notification permission denied');
                    showPermissionDeniedHelp();
                }
            } else {
                console.log('❌ This browser does not support notifications.');
                showNotificationNotSupported();
            }
        });

        // Setup soft permission request system
        function setupSoftPermissionRequest() {
            console.log('🔔 Setting up soft permission request');

            // Wait for user interaction before showing prompt
            let userInteracted = false;
            const interactionEvents = ['click', 'touch', 'keydown', 'scroll'];

            function handleUserInteraction() {
                if (!userInteracted) {
                    userInteracted = true;

                    // Remove event listeners
                    interactionEvents.forEach(event => {
                        document.removeEventListener(event, handleUserInteraction);
                    });

                    // Show soft prompt after a delay
                    setTimeout(() => {
                        if (Notification.permission === 'default') {
                            showSoftNotificationPrompt();
                        }
                    }, 5000); // Wait 5 seconds after first interaction
                }
            }

            // Add event listeners for user interaction
            interactionEvents.forEach(event => {
                document.addEventListener(event, handleUserInteraction, { once: true });
            });
        }

        function showSoftNotificationPrompt() {
            console.log('🔔 Showing soft notification prompt');

            // Create a non-intrusive notification prompt
            const promptHtml = `
                <div id="soft-notification-prompt" class="notification-prompt glass-card">
                    <div class="prompt-content">
                        <i class="fas fa-bell text-primary"></i>
                        <div class="prompt-text">
                            <h6>Stay Updated</h6>
                            <p>Get notified about important library updates, attendance alerts, and system notifications</p>
                        </div>
                        <div class="prompt-actions">
                            <button class="btn btn-sm btn-outline-secondary" onclick="dismissSoftPrompt()">
                                Maybe Later
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="requestNotificationPermission()">
                                Enable Notifications
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', promptHtml);

            // Animate in
            setTimeout(() => {
                const prompt = document.getElementById('soft-notification-prompt');
                if (prompt) {
                    prompt.classList.add('show');
                }
            }, 100);
        }

        function requestNotificationPermission() {
            console.log('🔔 Requesting notification permission...');

            Notification.requestPermission().then(function(permission) {
                console.log('📋 Permission result:', permission);

                if (permission === 'granted') {
                    console.log('✅ Notification permission granted');
                    dismissSoftPrompt();
                    showWelcomeNotification();
                    registerDeviceToken();

                    // Show success toast
                    if (window.pwaManager) {
                        window.pwaManager.showToast('Notifications enabled successfully!', 'success');
                    }
                } else if (permission === 'denied') {
                    console.log('❌ Notification permission denied');
                    dismissSoftPrompt();
                    showPermissionDeniedHelp();
                } else {
                    console.log('⚠️ Notification permission default/unknown');
                    dismissSoftPrompt();

                    if (window.pwaManager) {
                        window.pwaManager.showToast('Permission request unclear. Please try again.', 'warning');
                    }
                }
            }).catch(function(error) {
                console.error('❌ Error requesting permission:', error);
                dismissSoftPrompt();

                if (window.pwaManager) {
                    window.pwaManager.showToast('Failed to request notification permission', 'error');
                }
            });
        }

        function dismissSoftPrompt() {
            const prompt = document.getElementById('soft-notification-prompt');
            if (prompt) {
                prompt.classList.remove('show');
                setTimeout(() => {
                    prompt.remove();
                }, 300);
            }
        }

        function showWelcomeNotification() {
            console.log('🔔 Showing welcome notification');

            const notification = new Notification('Welcome to Librainian!', {
                body: 'You\'ll now receive important updates and alerts.',
                icon: '/static/img/ms-icon-512x512.png',
                badge: '/static/img/apple-touch-icon.png',
                tag: 'welcome-notification',
                requireInteraction: false,
                silent: false
            });

            // Auto-close after 5 seconds
            setTimeout(() => {
                notification.close();
            }, 5000);
        }

        function showPermissionDeniedHelp() {
            console.log('❌ Showing permission denied help');

            const helpHtml = `
                <div id="permission-help-prompt" class="notification-prompt glass-card">
                    <div class="prompt-content">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        <div class="prompt-text">
                            <h6>Notifications Blocked</h6>
                            <p>To enable notifications, click the lock icon in your browser's address bar and allow notifications for this site.</p>
                        </div>
                        <div class="prompt-actions">
                            <button class="btn btn-sm btn-primary" onclick="dismissPermissionHelp()">
                                Got it
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', helpHtml);

            setTimeout(() => {
                const prompt = document.getElementById('permission-help-prompt');
                if (prompt) {
                    prompt.classList.add('show');
                }
            }, 100);
        }

        function dismissPermissionHelp() {
            const prompt = document.getElementById('permission-help-prompt');
            if (prompt) {
                prompt.classList.remove('show');
                setTimeout(() => {
                    prompt.remove();
                }, 300);
            }
        }

        function showNotificationNotSupported() {
            console.log('❌ Showing notification not supported message');

            if (window.pwaManager) {
                window.pwaManager.showToast('Notifications are not supported in this browser', 'warning');
            }
        }

        function showNotification() {
            const options = {
                body: 'Welcome to Librainian! Your CRM tool for libraries and study centers.',
                icon: '/static/img/librainian-logo-black-transparent.png'
            };

            new Notification('Librainian Notification', options);
        }

        function registerDeviceToken() {
            console.log('🎫 Starting device token registration...');
            console.log('🔧 Messaging object:', messaging);
            console.log('🔑 Using VAPID key:', vapidKey);

            messaging.getToken({ vapidKey: vapidKey }).then((currentToken) => {
                console.log('📱 FCM getToken response:', currentToken);
                if (currentToken) {
                    console.log('✅ FCM Token received:', currentToken);
                    console.log('📏 Token length:', currentToken.length);
                    const deviceType = 'web';
                    saveDeviceToken(currentToken, deviceType);
                } else {
                    console.log('❌ No registration token available. Request permission to generate one.');
                    console.log('🔍 Check if service worker is registered and VAPID key is correct');
                }
            }).catch((err) => {
                console.error('❌ Error occurred while retrieving token:', err);
                console.error('🔍 Error details:', err.message, err.stack);
            });
        }

        function saveDeviceToken(token, deviceType) {
            console.log('💾 Saving device token to server...');
            console.log('🎫 Token:', token);
            console.log('📱 Device Type:', deviceType);

            const csrfToken = getCookie('csrftoken');
            console.log('🔐 CSRF Token:', csrfToken ? 'Found' : 'Not found');

            fetch('/librarian/save-device-token/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: `token=${encodeURIComponent(token)}&device_type=${encodeURIComponent(deviceType)}`
            })
            .then(response => {
                console.log('📡 Server response status:', response.status);
                console.log('📡 Server response ok:', response.ok);
                return response.json();
            })
            .then(data => {
                console.log('✅ Server response data:', data);
                console.log('💾 Device token saved successfully!');
                console.log('🎫 Final token:', token);

                // Show success message to user
                if (data.status === 'success' || data.success) {
                    console.log('🎉 Token registration successful!');
                } else {
                    console.log('⚠️ Token registration had issues:', data.message);
                }
            })
            .catch(error => {
                console.error('❌ Error saving device token:', error);
                console.error('🔍 Error details:', error.message, error.stack);
            });
        }

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Service Worker Registration with detailed logging
        if ('serviceWorker' in navigator) {
            console.log('🔧 Registering service worker...');
            navigator.serviceWorker
                .register('/firebase-messaging-sw.js')
                .then(function(registration) {
                    console.log('✅ Service Worker registered successfully!');
                    console.log('🔧 Registration scope:', registration.scope);
                    console.log('🔧 Registration state:', registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown');

                    // Set the service worker for messaging
                    messaging.useServiceWorker(registration);
                    console.log('🔧 Messaging service worker set');
                })
                .catch(function(err) {
                    console.error('❌ Service Worker registration failed:', err);
                    console.error('🔍 Error details:', err.message, err.stack);
                });
        } else {
            console.log('❌ Service Worker not supported in this browser');
        }
        