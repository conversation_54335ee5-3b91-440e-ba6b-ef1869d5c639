// Enhanced PWA Service Worker for Librainian
// Version: 3.0.0 - Enhanced with offline capabilities, performance optimization, and native-like features

const CACHE_VERSION = '3.0.0';
const CACHE_NAME = `librainian-v${CACHE_VERSION}`;
const CACHE_NAME_DYNAMIC = `librainian-dynamic-v${CACHE_VERSION}`;
const CACHE_NAME_OFFLINE = `librainian-offline-v${CACHE_VERSION}`;

// Core app shell resources (critical for app functionality)
const CORE_CACHE_RESOURCES = [
  '/',
  '/static/css/style.css',
  '/static/css/dark-mode.css',
  '/static/js/main.js',
  '/static/img/librainian-logo-black-transparent-med.png',
  '/static/img/ms-icon-512x512.png',
  '/static/img/apple-touch-icon.png',
  '/static/favicon.ico',
  '/static/audio/success.wav',
  '/static/audio/fail.wav',
  // Bootstrap and essential CDN resources
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css',
  'https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;600&display=swap'
];

// Scanner-specific resources (for scanner PWA)
const SCANNER_CACHE_RESOURCES = [
  '/attendance/scanner/',
  '/attendance/scanner-login/',
  '/attendance/today/',
  '/attendance/log/',
  'https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js'
];

// Offline fallback pages
const OFFLINE_FALLBACK_PAGE = '/offline.html';
const OFFLINE_FALLBACK_IMAGE = '/static/img/offline-placeholder.png';

// Network timeout for fetch requests
const NETWORK_TIMEOUT = 3000;

// Install event - Cache core resources
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker version:', CACHE_VERSION);

  event.waitUntil(
    Promise.all([
      // Cache core app shell
      caches.open(CACHE_NAME).then((cache) => {
        console.log('[SW] Caching core resources');
        return cache.addAll(CORE_CACHE_RESOURCES);
      }),
      // Cache scanner resources
      caches.open(CACHE_NAME_DYNAMIC).then((cache) => {
        console.log('[SW] Caching scanner resources');
        return cache.addAll(SCANNER_CACHE_RESOURCES);
      }),
      // Cache offline fallbacks
      caches.open(CACHE_NAME_OFFLINE).then((cache) => {
        console.log('[SW] Caching offline fallbacks');
        return cache.addAll([OFFLINE_FALLBACK_PAGE, OFFLINE_FALLBACK_IMAGE]);
      })
    ]).then(() => {
      console.log('[SW] Installation complete');
      // Force activation of new service worker
      return self.skipWaiting();
    }).catch((error) => {
      console.error('[SW] Installation failed:', error);
    })
  );
});

// Activate event - Clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker version:', CACHE_VERSION);

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName.startsWith('librainian-') &&
                !cacheName.includes(CACHE_VERSION)) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients
      self.clients.claim()
    ]).then(() => {
      console.log('[SW] Activation complete');
    })
  );
});

// Enhanced fetch event with offline-first strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests and chrome-extension requests
  if (request.method !== 'GET' || url.protocol === 'chrome-extension:') {
    return;
  }

  // Handle different types of requests with appropriate strategies
  if (isStaticAsset(request)) {
    // Static assets: Cache first with fallback
    event.respondWith(cacheFirstStrategy(request));
  } else if (isAPIRequest(request)) {
    // API requests: Network first with cache fallback
    event.respondWith(networkFirstStrategy(request));
  } else if (isHTMLRequest(request)) {
    // HTML pages: Network first with offline fallback
    event.respondWith(networkFirstWithOfflineFallback(request));
  } else {
    // Default: Network first
    event.respondWith(networkFirstStrategy(request));
  }
});

// Enhanced push notification handler
self.addEventListener('push', (event) => {
  console.log('[SW] Push notification received');

  let notificationData = {};

  try {
    if (event.data) {
      notificationData = event.data.json();
    }
  } catch (error) {
    console.error('[SW] Error parsing push data:', error);
    notificationData = {
      title: 'Librainian',
      body: event.data ? event.data.text() : 'New notification',
    };
  }

  const options = {
    body: notificationData.body || 'New notification from Librainian',
    icon: notificationData.icon || '/static/img/ms-icon-512x512.png',
    badge: notificationData.badge || '/static/img/apple-touch-icon.png',
    image: notificationData.image,
    data: notificationData.data || { url: '/' },
    actions: notificationData.actions || [
      {
        action: 'open',
        title: 'Open App',
        icon: '/static/img/apple-touch-icon.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/static/img/apple-touch-icon.png'
      }
    ],
    requireInteraction: notificationData.requireInteraction || false,
    silent: notificationData.silent || false,
    vibrate: notificationData.vibrate || [200, 100, 200],
    tag: notificationData.tag || 'librainian-notification',
    renotify: true
  };

  event.waitUntil(
    self.registration.showNotification(
      notificationData.title || 'Librainian',
      options
    )
  );
});

// Enhanced notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('[SW] Notification clicked:', event.notification.tag);

  event.notification.close();

  const action = event.action;
  const notificationData = event.notification.data || {};

  if (action === 'dismiss') {
    return; // Just close the notification
  }

  const urlToOpen = action === 'open' || !action
    ? (notificationData.url || '/')
    : '/';

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Check if app is already open
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            client.focus();
            if (urlToOpen !== '/') {
              client.navigate(urlToOpen);
            }
            return;
          }
        }

        // Open new window if app is not open
        if (clients.openWindow) {
          return clients.openWindow(urlToOpen);
        }
      })
  );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('[SW] Background sync triggered:', event.tag);

  if (event.tag === 'attendance-sync') {
    event.waitUntil(syncAttendanceData());
  } else if (event.tag === 'notification-sync') {
    event.waitUntil(syncNotificationData());
  }
});

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);

  const { type, payload } = event.data;

  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    case 'GET_VERSION':
      event.ports[0].postMessage({ version: CACHE_VERSION });
      break;
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
    case 'CACHE_URLS':
      cacheUrls(payload.urls).then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
    default:
      console.log('[SW] Unknown message type:', type);
  }
});

// Utility functions for caching strategies

function isStaticAsset(request) {
  const url = new URL(request.url);
  return url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

function isAPIRequest(request) {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/') ||
         url.pathname.includes('/ajax/') ||
         url.pathname.includes('/json/');
}

function isHTMLRequest(request) {
  const acceptHeader = request.headers.get('Accept') || '';
  return acceptHeader.includes('text/html');
}

// Cache-first strategy for static assets
async function cacheFirstStrategy(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetchWithTimeout(request);

    // Cache successful responses
    if (networkResponse.status === 200) {
      const cache = await caches.open(CACHE_NAME_DYNAMIC);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('[SW] Cache-first strategy failed:', error);

    // Return offline fallback for images
    if (request.destination === 'image') {
      const offlineCache = await caches.open(CACHE_NAME_OFFLINE);
      return offlineCache.match(OFFLINE_FALLBACK_IMAGE);
    }

    throw error;
  }
}

// Network-first strategy for API requests
async function networkFirstStrategy(request) {
  try {
    const networkResponse = await fetchWithTimeout(request);

    // Cache successful GET responses
    if (request.method === 'GET' && networkResponse.status === 200) {
      const cache = await caches.open(CACHE_NAME_DYNAMIC);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('[SW] Network request failed, trying cache:', error);

    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    throw error;
  }
}

// Network-first with offline fallback for HTML pages
async function networkFirstWithOfflineFallback(request) {
  try {
    const networkResponse = await fetchWithTimeout(request);

    // Cache successful responses
    if (networkResponse.status === 200) {
      const cache = await caches.open(CACHE_NAME_DYNAMIC);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('[SW] Network request failed for HTML page:', error);

    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline fallback page
    const offlineCache = await caches.open(CACHE_NAME_OFFLINE);
    return offlineCache.match(OFFLINE_FALLBACK_PAGE);
  }
}

// Fetch with timeout
function fetchWithTimeout(request, timeout = NETWORK_TIMEOUT) {
  return Promise.race([
    fetch(request),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Network timeout')), timeout)
    )
  ]);
}

// Background sync functions
async function syncAttendanceData() {
  console.log('[SW] Syncing attendance data');

  try {
    // Get pending attendance data from IndexedDB
    const pendingData = await getPendingAttendanceData();

    for (const data of pendingData) {
      try {
        const response = await fetch('/api/attendance/sync/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });

        if (response.ok) {
          await removePendingAttendanceData(data.id);
          console.log('[SW] Synced attendance data:', data.id);
        }
      } catch (error) {
        console.error('[SW] Failed to sync attendance data:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Background sync failed:', error);
  }
}

async function syncNotificationData() {
  console.log('[SW] Syncing notification data');
  // Implementation for notification sync
}

// IndexedDB helpers (simplified - you may want to use a library like Dexie.js)
async function getPendingAttendanceData() {
  // This would typically use IndexedDB to get pending data
  return [];
}

async function removePendingAttendanceData(id) {
  // This would typically remove the synced data from IndexedDB
  console.log('[SW] Removing synced data:', id);
}

// Utility functions for cache management
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  return Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}

async function cacheUrls(urls) {
  const cache = await caches.open(CACHE_NAME_DYNAMIC);
  return cache.addAll(urls);
}

// Performance monitoring
function logPerformance(label, startTime) {
  const duration = Date.now() - startTime;
  console.log(`[SW] ${label} took ${duration}ms`);
}

// Error reporting
function reportError(error, context) {
  console.error(`[SW] Error in ${context}:`, error);

  // You could send this to an error reporting service
  // fetch('/api/errors/', {
  //   method: 'POST',
  //   body: JSON.stringify({ error: error.message, context, stack: error.stack })
  // }).catch(() => {}); // Fail silently
}

console.log('[SW] Service Worker loaded successfully, version:', CACHE_VERSION);