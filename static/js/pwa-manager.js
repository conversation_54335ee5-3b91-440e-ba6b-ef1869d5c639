/**
 * Enhanced PWA Manager for Librainian
 * Handles PWA installation, notifications, offline capabilities, and performance optimization
 * Version: 3.0.0
 */

class PWAManager {
    constructor() {
        this.version = '3.0.0';
        this.serviceWorkerRegistration = null;
        this.deferredPrompt = null;
        this.isOnline = navigator.onLine;
        this.notificationPermission = 'default';
        this.installPromptShown = false;
        
        this.init();
    }

    async init() {
        console.log('[PWA] Initializing PWA Manager v' + this.version);
        
        // Register service worker
        await this.registerServiceWorker();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Check for updates
        this.checkForUpdates();
        
        // Initialize notification system with soft ask
        this.initNotificationSystem();
        
        // Setup install prompt
        this.setupInstallPrompt();
        
        // Setup offline detection
        this.setupOfflineDetection();
        
        console.log('[PWA] PWA Manager initialized successfully');
    }

    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.serviceWorkerRegistration = await navigator.serviceWorker.register(
                    '/static/js/service_worker.js',
                    { scope: '/' }
                );
                
                console.log('[PWA] Service Worker registered:', this.serviceWorkerRegistration.scope);
                
                // Listen for service worker messages
                navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage.bind(this));
                
                return this.serviceWorkerRegistration;
            } catch (error) {
                console.error('[PWA] Service Worker registration failed:', error);
                return null;
            }
        } else {
            console.warn('[PWA] Service Workers not supported');
            return null;
        }
    }

    setupEventListeners() {
        // Install prompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('[PWA] Install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallBanner();
        });

        // App installed event
        window.addEventListener('appinstalled', () => {
            console.log('[PWA] App installed successfully');
            this.hideInstallBanner();
            this.showToast('App installed successfully!', 'success');
        });

        // Visibility change for performance optimization
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.checkForUpdates();
            }
        });
    }

    async checkForUpdates() {
        if (this.serviceWorkerRegistration) {
            try {
                await this.serviceWorkerRegistration.update();
                console.log('[PWA] Checked for service worker updates');
            } catch (error) {
                console.error('[PWA] Update check failed:', error);
            }
        }
    }

    // Soft notification permission request
    async initNotificationSystem() {
        if (!('Notification' in window)) {
            console.warn('[PWA] Notifications not supported');
            return;
        }

        this.notificationPermission = Notification.permission;
        
        // Don't immediately ask for permission - wait for user interaction
        if (this.notificationPermission === 'default') {
            this.showNotificationPrompt();
        } else if (this.notificationPermission === 'granted') {
            console.log('[PWA] Notification permission already granted');
        }
    }

    showNotificationPrompt() {
        // Create a subtle, non-intrusive notification prompt
        const promptHtml = `
            <div id="notification-prompt" class="notification-prompt glass-card">
                <div class="prompt-content">
                    <i class="fas fa-bell text-primary"></i>
                    <div class="prompt-text">
                        <h6>Stay Updated</h6>
                        <p>Get notified about important updates and attendance alerts</p>
                    </div>
                    <div class="prompt-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="pwaManager.dismissNotificationPrompt()">
                            Maybe Later
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="pwaManager.requestNotificationPermission()">
                            Enable
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Show prompt after user has interacted with the app
        setTimeout(() => {
            if (this.notificationPermission === 'default' && !this.installPromptShown) {
                document.body.insertAdjacentHTML('beforeend', promptHtml);
                this.animatePromptIn('notification-prompt');
            }
        }, 10000); // Wait 10 seconds before showing
    }

    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            this.notificationPermission = permission;
            
            if (permission === 'granted') {
                this.showToast('Notifications enabled successfully!', 'success');
                this.dismissNotificationPrompt();
                
                // Show a welcome notification
                this.showNotification('Welcome to Librainian!', {
                    body: 'You\'ll now receive important updates and alerts.',
                    icon: '/static/img/ms-icon-512x512.png'
                });
            } else {
                this.showToast('Notifications disabled. You can enable them later in settings.', 'info');
                this.dismissNotificationPrompt();
            }
        } catch (error) {
            console.error('[PWA] Notification permission request failed:', error);
            this.showToast('Failed to enable notifications', 'error');
        }
    }

    dismissNotificationPrompt() {
        const prompt = document.getElementById('notification-prompt');
        if (prompt) {
            this.animatePromptOut('notification-prompt');
        }
    }

    // Install prompt management
    showInstallBanner() {
        if (this.installPromptShown) return;
        
        const bannerHtml = `
            <div id="install-banner" class="install-banner glass-card">
                <div class="banner-content">
                    <i class="fas fa-download text-primary"></i>
                    <div class="banner-text">
                        <h6>Install Librainian</h6>
                        <p>Get the full app experience with offline access</p>
                    </div>
                    <div class="banner-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="pwaManager.dismissInstallBanner()">
                            Not Now
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="pwaManager.installApp()">
                            Install
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', bannerHtml);
        this.animatePromptIn('install-banner');
        this.installPromptShown = true;
    }

    async installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log('[PWA] Install prompt outcome:', outcome);
            
            if (outcome === 'accepted') {
                this.showToast('Installing app...', 'info');
            }
            
            this.deferredPrompt = null;
            this.hideInstallBanner();
        }
    }

    dismissInstallBanner() {
        this.hideInstallBanner();
        // Don't show again for this session
        sessionStorage.setItem('installBannerDismissed', 'true');
    }

    hideInstallBanner() {
        const banner = document.getElementById('install-banner');
        if (banner) {
            this.animatePromptOut('install-banner');
        }
    }

    // Offline detection and handling
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showToast('Back online!', 'success');
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showToast('You\'re offline. Some features may be limited.', 'warning');
        });
    }

    async syncOfflineData() {
        if (this.serviceWorkerRegistration && this.serviceWorkerRegistration.sync) {
            try {
                await this.serviceWorkerRegistration.sync.register('attendance-sync');
                console.log('[PWA] Background sync registered');
            } catch (error) {
                console.error('[PWA] Background sync registration failed:', error);
            }
        }
    }

    // Notification helpers
    showNotification(title, options = {}) {
        if (this.notificationPermission === 'granted') {
            const defaultOptions = {
                icon: '/static/img/ms-icon-512x512.png',
                badge: '/static/img/apple-touch-icon.png',
                vibrate: [200, 100, 200],
                tag: 'librainian-notification'
            };
            
            new Notification(title, { ...defaultOptions, ...options });
        }
    }

    // Service worker message handler
    handleServiceWorkerMessage(event) {
        const { type, payload } = event.data;
        
        switch (type) {
            case 'UPDATE_AVAILABLE':
                this.showUpdatePrompt();
                break;
            case 'CACHE_UPDATED':
                console.log('[PWA] Cache updated');
                break;
            default:
                console.log('[PWA] Unknown message from service worker:', type);
        }
    }

    showUpdatePrompt() {
        const updateHtml = `
            <div id="update-prompt" class="update-prompt glass-card">
                <div class="prompt-content">
                    <i class="fas fa-sync-alt text-primary"></i>
                    <div class="prompt-text">
                        <h6>Update Available</h6>
                        <p>A new version of the app is available</p>
                    </div>
                    <div class="prompt-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="pwaManager.dismissUpdatePrompt()">
                            Later
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="pwaManager.applyUpdate()">
                            Update Now
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', updateHtml);
        this.animatePromptIn('update-prompt');
    }

    async applyUpdate() {
        if (this.serviceWorkerRegistration && this.serviceWorkerRegistration.waiting) {
            this.serviceWorkerRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            window.location.reload();
        }
    }

    dismissUpdatePrompt() {
        const prompt = document.getElementById('update-prompt');
        if (prompt) {
            this.animatePromptOut('update-prompt');
        }
    }

    // Animation helpers
    animatePromptIn(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.3s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 100);
        }
    }

    animatePromptOut(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.transition = 'all 0.3s ease';
            element.style.opacity = '0';
            element.style.transform = 'translateY(-20px)';
            
            setTimeout(() => {
                element.remove();
            }, 300);
        }
    }

    // Toast notification system
    showToast(message, type = 'info') {
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="pwa-toast toast-${type}">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', toastHtml);
        
        const toast = document.getElementById(toastId);
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Performance monitoring
    measurePerformance(label, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`[PWA] ${label} took ${end - start} milliseconds`);
        return result;
    }

    // Cache management
    async clearCache() {
        if (this.serviceWorkerRegistration) {
            const messageChannel = new MessageChannel();
            
            return new Promise((resolve) => {
                messageChannel.port1.onmessage = (event) => {
                    resolve(event.data.success);
                };
                
                this.serviceWorkerRegistration.active.postMessage(
                    { type: 'CLEAR_CACHE' },
                    [messageChannel.port2]
                );
            });
        }
    }

    // Get app info
    getAppInfo() {
        return {
            version: this.version,
            isOnline: this.isOnline,
            notificationPermission: this.notificationPermission,
            serviceWorkerRegistered: !!this.serviceWorkerRegistration,
            installPromptAvailable: !!this.deferredPrompt
        };
    }
}

// Initialize PWA Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PWAManager;
}
