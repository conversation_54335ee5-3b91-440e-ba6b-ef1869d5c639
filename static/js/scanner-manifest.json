{"name": "Libra<PERSON>", "short_name": "Scanner", "description": "Dedicated QR code attendance scanner for Librainian library management system", "start_url": "/attendance/scanner/", "scope": "/attendance/", "display": "standalone", "orientation": "portrait-primary", "background_color": "#ffffff", "theme_color": "#10b981", "lang": "en", "dir": "ltr", "prefer_related_applications": false, "display_override": ["fullscreen", "standalone", "minimal-ui"], "icons": [{"src": "/static/img/ms-icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "/static/img/ms-icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}, {"src": "/static/img/apple-touch-icon.png", "sizes": "180x180", "type": "image/png", "purpose": "any"}, {"src": "/static/img/apple-touch-icon.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/static/img/apple-touch-icon.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/static/img/apple-touch-icon.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}], "shortcuts": [{"name": "Scanner", "short_name": "<PERSON><PERSON>", "description": "Start QR code scanning", "url": "/attendance/scanner/", "icons": [{"src": "/static/img/apple-touch-icon.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Today's Attendance", "short_name": "Today", "description": "View today's attendance", "url": "/attendance/today/", "icons": [{"src": "/static/img/apple-touch-icon.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Attendance Log", "short_name": "Log", "description": "View attendance history", "url": "/attendance/log/", "icons": [{"src": "/static/img/apple-touch-icon.png", "sizes": "96x96", "type": "image/png"}]}], "categories": ["education", "productivity", "utilities"], "screenshots": [{"src": "/static/img/screenshots/scanner_mobile.png", "sizes": "1080x1920", "type": "image/png", "form_factor": "narrow", "label": "QR code scanner interface"}], "related_applications": [], "launch_handler": {"client_mode": "focus-existing"}, "handle_links": "preferred"}