/**
 * PWA Enhanced Styles for Librainian
 * Includes styles for install prompts, notifications, offline indicators, and native-like UI
 * Version: 3.0.0
 */

/* PWA Install Banner */
.install-banner {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1050;
    max-width: 400px;
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.9);
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
    transition: all 0.3s ease;
}

.install-banner.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.banner-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.banner-content i {
    font-size: 24px;
    color: #6366f1;
}

.banner-text {
    flex: 1;
}

.banner-text h6 {
    margin: 0 0 4px 0;
    font-weight: 600;
    color: #1f2937;
}

.banner-text p {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
}

.banner-actions {
    display: flex;
    gap: 8px;
}

.banner-actions .btn {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
}

/* Notification Prompt */
.notification-prompt {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 350px;
    width: 90%;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.9);
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

.notification-prompt.show {
    opacity: 1;
    transform: translateY(0);
}

.prompt-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.prompt-content i {
    font-size: 20px;
    color: #6366f1;
    margin-top: 2px;
}

.prompt-text {
    flex: 1;
}

.prompt-text h6 {
    margin: 0 0 4px 0;
    font-weight: 600;
    color: #1f2937;
}

.prompt-text p {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #6b7280;
    line-height: 1.4;
}

.prompt-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.prompt-actions .btn {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
}

/* Update Prompt */
.update-prompt {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1060;
    max-width: 400px;
    width: 90%;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.95);
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
    transition: all 0.3s ease;
}

.update-prompt.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

/* Toast Notifications */
.pwa-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 300px;
}

.pwa-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.pwa-toast.toast-success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.pwa-toast.toast-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.pwa-toast.toast-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.pwa-toast.toast-info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

/* Offline Indicator */
.offline-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    text-align: center;
    padding: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1100;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.offline-indicator.show {
    transform: translateY(0);
}

/* PWA Loading States */
.pwa-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.pwa-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: pwa-spin 1s linear infinite;
}

@keyframes pwa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Glass Effects for PWA Components */
.glass-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Dark Mode Support for PWA Components */
.dark-mode .install-banner,
.dark-mode .notification-prompt,
.dark-mode .update-prompt,
.dark-mode .glass-card {
    background: rgba(17, 24, 39, 0.9);
    border: 1px solid rgba(75, 85, 99, 0.3);
    color: #f9fafb;
}

.dark-mode .banner-text h6,
.dark-mode .prompt-text h6 {
    color: #f9fafb;
}

.dark-mode .banner-text p,
.dark-mode .prompt-text p {
    color: #d1d5db;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .install-banner,
    .notification-prompt {
        left: 10px;
        right: 10px;
        width: auto;
        max-width: none;
        transform: none;
    }
    
    .install-banner {
        bottom: 10px;
        left: 10px;
        right: 10px;
        transform: translateY(20px);
    }
    
    .install-banner.show {
        transform: translateY(0);
    }
    
    .notification-prompt {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-20px);
    }
    
    .notification-prompt.show {
        transform: translateY(0);
    }
    
    .update-prompt {
        left: 10px;
        right: 10px;
        width: auto;
        max-width: none;
        transform: translate(0, -50%) scale(0.9);
    }
    
    .update-prompt.show {
        transform: translate(0, -50%) scale(1);
    }
    
    .pwa-toast {
        left: 10px;
        right: 10px;
        max-width: none;
        transform: translateY(-100%);
    }
    
    .pwa-toast.show {
        transform: translateY(0);
    }
}

/* PWA Status Bar */
.pwa-status-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: var(--theme-color, #6366f1);
    z-index: 1200;
    display: none;
}

/* Show status bar when app is installed */
@media (display-mode: standalone) {
    .pwa-status-bar {
        display: block;
    }
    
    body {
        padding-top: 20px;
    }
}

/* Enhanced Button Styles for PWA */
.btn-pwa {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    border: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-pwa:hover {
    background: linear-gradient(135deg, #4f46e5, #4338ca);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
    color: white;
}

.btn-pwa:active {
    transform: translateY(0);
}

/* Scanner PWA Specific Styles */
.scanner-pwa-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    padding: 20px;
}

.scanner-pwa-header {
    text-align: center;
    margin-bottom: 30px;
}

.scanner-pwa-header h1 {
    color: #0f172a;
    font-weight: 700;
    margin-bottom: 8px;
}

.scanner-pwa-header p {
    color: #64748b;
    font-size: 16px;
}

/* Performance Optimizations */
.pwa-optimized {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .install-banner,
    .notification-prompt,
    .update-prompt,
    .pwa-toast {
        transition: none;
    }
    
    .pwa-spinner {
        animation: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .install-banner,
    .notification-prompt,
    .update-prompt {
        border: 2px solid currentColor;
        background: white;
        color: black;
    }
    
    .dark-mode .install-banner,
    .dark-mode .notification-prompt,
    .dark-mode .update-prompt {
        background: black;
        color: white;
    }
}
