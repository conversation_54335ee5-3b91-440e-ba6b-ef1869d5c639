<style>
    .modern-sidebar {
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: var(--bg-primary);
        border-right: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    /* Mobile sidebar height adjustment */
    @media (max-width: 991.98px) {
        .modern-sidebar {
            height: calc(100vh - var(--bottom-menu-height, 70px)) !important;
        }
    }

    /* Dark Mode Sidebar */
    body.dark-mode .modern-sidebar {
        background: var(--bg-secondary);
        border-right-color: var(--border-color);
    }

    .sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        flex-shrink: 0;
        position: relative;
        z-index: 1;
        transition: all 0.3s ease;
    }

    /* Dark Mode Sidebar Header */
    body.dark-mode .sidebar-header {
        border-bottom-color: var(--border-color);
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    }

    .sidebar-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: white;
        padding: 0;
        width: 100%;
    }

    .sidebar-logo:hover {
        color: white;
    }

    .logo-image {
        max-width: 100%;
        height: auto;
        max-height: 40px;
        object-fit: contain;
        filter: brightness(0) invert(1);
    }

    .logo-text {
        font-family: 'Comfortaa', sans-serif;
        font-weight: 600;
        font-size: 1.25rem;
        margin: 0;
    }

    .sidebar-content {
        flex: 1;
        padding: 1rem 0;
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        min-height: 0;
        position: relative;
    }

    /* Mobile sidebar content adjustment */
    @media (max-width: 991.98px) {
        .sidebar-content {
            overflow-y: auto !important;
        }
    }

    .sidebar-content::-webkit-scrollbar {
        display: none;
    }

    .sidebar-section {
        margin-bottom: 2rem;
    }

    .sidebar-section-title {
        padding: 0 1.5rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--text-muted);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin: 0;
        transition: color 0.3s ease;
    }

    /* Dark Mode Sidebar Section Title */
    body.dark-mode .sidebar-section-title {
        color: var(--text-muted);
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu-item {
        margin: 0.25rem 0;
    }

    .sidebar-menu-link {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1.5rem;
        color: var(--text-secondary);
        text-decoration: none;
        transition: var(--transition);
        border-radius: 0;
        position: relative;
        font-weight: 500;
        font-size: 0.875rem;
    }

    .sidebar-menu-link:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
    }

    /* Dark Mode Sidebar Menu Links */
    body.dark-mode .sidebar-menu-link {
        color: var(--text-secondary);
    }

    body.dark-mode .sidebar-menu-link:hover {
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
    }

    .sidebar-menu-link.active {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
        position: relative;
    }

    /* Dark Mode Active Sidebar Link */
    body.dark-mode .sidebar-menu-link.active {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
    }

    .sidebar-menu-link.active::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: white;
    }

    .sidebar-menu-icon {
        width: 20px;
        text-align: center;
        font-size: 1rem;
    }

    .sidebar-menu-text {
        flex: 1;
    }

    .sidebar-menu-badge {
        background: var(--danger);
        color: white;
        font-size: 0.75rem;
        padding: 0.125rem 0.5rem;
        border-radius: 10px;
        font-weight: 600;
    }

    /* Submenu Styles */
    .sidebar-submenu {
        list-style: none;
        padding: 0;
        margin: 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        background: rgba(0, 0, 0, 0.1);
    }

    .sidebar-submenu.show {
        max-height: 200px;
    }

    .sidebar-submenu-item {
        margin: 0;
    }

    .sidebar-submenu-link {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem 1.5rem 0.5rem 3rem;
        color: var(--text-secondary);
        text-decoration: none;
        transition: var(--transition);
        font-weight: 400;
        font-size: 0.8rem;
        border-left: 2px solid transparent;
    }

    .sidebar-submenu-link:hover {
        background: var(--bg-tertiary);
        color: var(--text-primary);
        border-left-color: var(--primary);
    }

    .sidebar-submenu-link.active {
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
        border-left-color: var(--primary);
    }

    /* Dark Mode Submenu */
    body.dark-mode .sidebar-submenu {
        background: rgba(255, 255, 255, 0.02);
    }

    body.dark-mode .sidebar-submenu-link {
        color: var(--text-secondary);
    }

    body.dark-mode .sidebar-submenu-link:hover {
        background: rgba(255, 255, 255, 0.05);
        color: var(--text-primary);
    }

    body.dark-mode .sidebar-submenu-link.active {
        background: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
    }

    .sidebar-menu-arrow {
        margin-left: auto;
        transition: transform 0.3s ease;
        font-size: 0.75rem;
    }

    .sidebar-menu-arrow.rotated {
        transform: rotate(90deg);
    }

    .sidebar-close-btn {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
    }

    .sidebar-close-btn:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .sidebar-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid var(--border-color);
        background: var(--bg-primary);
        flex-shrink: 0;
        height: 80px; /* Fixed height */
        box-sizing: border-box;
        position: relative;
    }

    /* Mobile sidebar footer adjustment */
    @media (max-width: 991.98px) {
        .sidebar-footer {
            background: var(--bg-primary) !important;
        }
    }

    .sidebar-user {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: var(--bg-tertiary);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
    }

    .sidebar-user-avatar {
        width: 36px;
        height: 36px;
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .sidebar-user-info {
        flex: 1;
        min-width: 0;
    }

    .sidebar-user-name {
        font-weight: 600;
        color: var(--gray-900);
        margin: 0;
        font-size: 0.875rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .sidebar-user-role {
        font-size: 0.75rem;
        color: var(--gray-500);
        margin: 0;
    }

    /* Dark Mode Sidebar Footer */
    body.dark-mode .sidebar-footer {
        border-top-color: var(--border-color);
        background: var(--bg-secondary);
    }

    body.dark-mode .sidebar-user {
        background: var(--bg-tertiary);
        border-color: var(--border-color);
    }

    body.dark-mode .sidebar-user-name {
        color: var(--text-primary);
    }

    body.dark-mode .sidebar-user-role {
        color: var(--text-muted);
    }

    /* Mobile Sidebar Specific */
    .sidebar-mobile .sidebar-header {
        padding-right: 3rem;
        position: relative;
    }

    /* Responsive */
    @media (max-width: 991.98px) {
        .sidebar-desktop .sidebar-close-btn {
            display: none;
        }
    }
</style>

<div class="modern-sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <a href="/{{ role }}/dashboard/" class="sidebar-logo">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" class="logo-image">
        </a>

        <!-- Close button for mobile -->
        <button class="sidebar-close-btn d-lg-none" onclick="closeMobileSidebar()">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- Sidebar Content -->
    <div class="sidebar-content custom-scrollbar">
        <!-- Main Navigation -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">Main</h6>
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/dashboard/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-tachometer-alt sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Dashboard</span>
                    </a>
                </li>
                
                {% if role == "librarian" %}
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/analytics/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-chart-line sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Analytics</span>
                    </a>
                </li>
                {% endif %}
                
                <li class="sidebar-menu-item">
                    <a href="#" class="sidebar-menu-link" onclick="toggleSubmenu(this, event)">
                        <i class="fas fa-clipboard-list sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Register</span>
                        <i class="fas fa-chevron-right sidebar-menu-arrow"></i>
                    </a>
                    <ul class="sidebar-submenu">
                        <li class="sidebar-submenu-item">
                            <a href="/{{ role }}/student-register/" class="sidebar-submenu-link" onclick="setActiveLink(this)">
                                <i class="fas fa-users sidebar-menu-icon"></i>
                                <span class="sidebar-menu-text">Student Register</span>
                            </a>
                        </li>
                        <li class="sidebar-submenu-item">
                            <a href="/{{ role }}/invoice-register/" class="sidebar-submenu-link" onclick="setActiveLink(this)">
                                <i class="fas fa-file-invoice sidebar-menu-icon"></i>
                                <span class="sidebar-menu-text">Invoice Register</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <!-- Students Section -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">Students</h6>
            <ul class="sidebar-menu">
                {% if role == "librarian" %}
                <li class="sidebar-menu-item">
                    <a href="/students/temp_students_list/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-clock sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Pending Students</span>
                        <span class="sidebar-menu-badge">New</span>
                    </a>
                </li>
                {% elif role == "sublibrarian" %}
                <li class="sidebar-menu-item">
                    <a href="/students/temp_subLib_students_list/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-clock sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Pending Students</span>
                    </a>
                </li>
                {% endif %}

                <li class="sidebar-menu-item">
                    <a href="/students/create/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-user-plus sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Add Student</span>
                    </a>
                </li>

                <li class="sidebar-menu-item">
                    <a href="/students/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-money-bill-wave sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Fees Due</span>
                    </a>
                </li>

                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/partial-payments/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-coins sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Partial Payments</span>
                        <span class="sidebar-menu-badge">New</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Attendance Section -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">Attendance</h6>
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:scanner_page' %}" class="sidebar-menu-link" onclick="setActiveLink(this)" target="_blank">
                        <i class="fas fa-qrcode sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Attendance Scanner</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:today_attendance' %}" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-calendar-day sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Today</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:attendance_log' %}" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-history sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Attendance Log</span>
                    </a>
                </li>
                {% if role == "librarian" %}
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:manage_scanner_accounts' %}" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-users-cog sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Scanner Accounts</span>
                        <span class="sidebar-menu-badge">New</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </div>

        <!-- Management Section -->
        {% if role == "librarian" %}
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">Management</h6>
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/marketing/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-bullhorn sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Marketing</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/visitors/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-user-friends sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Visitors</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/daily-transaction/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-money-bill sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Transactions</span>
                    </a>
                </li>

                <li class="sidebar-menu-item">
                    <a href="/sublibrarian/signup/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-user-plus sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Add Sub-librarian</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/shifts/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-clock sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Manage Shifts</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/seats/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-chair sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Seat Management</span>
                    </a>
                </li>
            </ul>
        </div>
        {% elif role == "sublibrarian" %}
        <!-- Attendance Section for Sublibrarian -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">Attendance</h6>
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:scanner_page' %}" class="sidebar-menu-link" onclick="setActiveLink(this)" target="_blank">
                        <i class="fas fa-qrcode sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Attendance Scanner</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:today_attendance' %}" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-calendar-day sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Today</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:attendance_log' %}" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-history sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Attendance Log</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="{% url 'attendance:manage_scanner_accounts' %}" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-users-cog sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Scanner Accounts</span>
                        <span class="sidebar-menu-badge">New</span>
                    </a>
                </li>
            </ul>
        </div>

        <div class="sidebar-section">
            <h6 class="sidebar-section-title">Management</h6>
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/daily-transaction/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-money-bill sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Daily Transactions</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/seats/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-chair sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Seat Management</span>
                    </a>
                </li>
            </ul>
        </div>
        {% endif %}

        <!-- System Section -->
        <div class="sidebar-section">
            <h6 class="sidebar-section-title">System</h6>
            <ul class="sidebar-menu">
                <li class="sidebar-menu-item">
                    <a href="/membership/plans/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-crown sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Membership</span>
                    </a>
                </li>

                {% if role == "librarycommander" %}
                <li class="sidebar-menu-item">
                    <a href="/blogs/coupons/create_coupon/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-ticket-alt sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Coupons</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/blogs/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-blog sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Blog Section</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/complaint_dashboard/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-exclamation-circle sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Complaints</span>
                    </a>
                </li>
                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/logs/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-file-alt sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">System Logs</span>
                    </a>
                </li>
                {% endif %}

                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/profile/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-user sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Profile</span>
                    </a>
                </li>

                <li class="sidebar-menu-item">
                    <a href="/{{ role }}/help/" class="sidebar-menu-link" onclick="setActiveLink(this)">
                        <i class="fas fa-question-circle sidebar-menu-icon"></i>
                        <span class="sidebar-menu-text">Help & Support</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="sidebar-user">
            <div class="sidebar-user-avatar">
                {% if user.is_authenticated %}{{ user.username.0|upper }}{% else %}U{% endif %}
            </div>
            <div class="sidebar-user-info">
                <h6 class="sidebar-user-name">{% if user.is_authenticated %}{{ user.username }}{% else %}User{% endif %}</h6>
                <p class="sidebar-user-role">{{ role|title|default:"User" }}</p>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle submenu functionality
    function toggleSubmenu(element, event) {
        event.preventDefault();

        const submenu = element.nextElementSibling;
        const arrow = element.querySelector('.sidebar-menu-arrow');

        if (submenu && submenu.classList.contains('sidebar-submenu')) {
            submenu.classList.toggle('show');
            arrow.classList.toggle('rotated');
        }

        return false;
    }

    // Set active link functionality
    function setActiveLink(element) {
        // Remove active class from all links
        document.querySelectorAll('.sidebar-menu-link, .sidebar-submenu-link').forEach(link => {
            link.classList.remove('active');
        });

        // Add active class to clicked link
        element.classList.add('active');

        // Store active link in localStorage
        localStorage.setItem('activeSidebarLink', element.getAttribute('href'));

        // Close mobile sidebar after navigation
        if (window.innerWidth < 992) {
            setTimeout(() => {
                closeMobileSidebar();
            }, 100);
        }

        // Allow default navigation to proceed
        return true;
    }

    // Set active link on page load
    document.addEventListener('DOMContentLoaded', function() {
        const currentPath = window.location.pathname;
        const allLinks = document.querySelectorAll('.sidebar-menu-link, .sidebar-submenu-link');

        allLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');

                // If it's a submenu link, open the parent submenu
                if (link.classList.contains('sidebar-submenu-link')) {
                    const submenu = link.closest('.sidebar-submenu');
                    const parentLink = submenu.previousElementSibling;
                    const arrow = parentLink.querySelector('.sidebar-menu-arrow');

                    submenu.classList.add('show');
                    arrow.classList.add('rotated');
                }
            }
        });
    });

    // Open profile page
    function openProfilePage() {
          // Navigate directly to profile page
        window.location.href = '/{{ role }}/profile/';
    }

    // Open help page
    function openHelpPage() {
        // Create a modal or navigate to help page
        if (window.modernDashboard) {
            window.modernDashboard.loadPage('help');
        } else {
            // Fallback: open in new content area
            loadHelpContent();
        }
    }

    // Load profile content dynamically
    function loadProfileContent() {
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            fetch('/api/profile-content/')
                .then(response => response.text())
                .then(html => {
                    mainContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading profile:', error);
                    // Fallback to existing profile template
                    window.location.href = '/{{ role }}/profile/';
                });
        }
    }

    // Load help content dynamically
    function loadHelpContent() {
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            fetch('/api/help-content/')
                .then(response => response.text())
                .then(html => {
                    mainContent.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading help:', error);
                    // Fallback to existing help template
                    window.location.href = '/{{ role }}/help/';
                });
        }
    }

    // Fallback function for mobile sidebar close
    function closeMobileSidebar() {
        if (window.modernDashboard && window.modernDashboard.closeMobileSidebar) {
            window.modernDashboard.closeMobileSidebar();
        } else {
            // Fallback: hide sidebar manually
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.style.transform = 'translateX(-100%)';
            }
            // Remove overlay if it exists
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) {
                overlay.remove();
            }
        }
    }
</script>
