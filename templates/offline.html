<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Librainian</title>
    <meta name="theme-color" content="#6366f1">
    
    <!-- Inline critical CSS for offline page -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .offline-container {
            max-width: 500px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            font-size: 80px;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            color: #ffffff;
        }

        .offline-message {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 200px;
            justify-content: center;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .connection-status {
            margin-top: 30px;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ef4444;
            animation: pulse 2s infinite;
        }

        .status-dot.online {
            background: #10b981;
            animation: none;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .offline-features {
            margin-top: 30px;
            text-align: left;
        }

        .offline-features h3 {
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .feature-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            font-size: 16px;
        }

        .retry-animation {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .offline-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .offline-icon {
                font-size: 60px;
            }

            .offline-title {
                font-size: 24px;
            }

            .offline-message {
                font-size: 14px;
            }

            .btn {
                min-width: 180px;
                padding: 10px 20px;
                font-size: 14px;
            }
        }

        /* Dark mode detection */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            }
        }

        /* Reduced motion */
        @media (prefers-reduced-motion: reduce) {
            .status-dot {
                animation: none;
            }
            
            .retry-animation {
                animation: none;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry, some features are still available offline.
        </p>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="retryConnection()">
                <span id="retry-icon">🔄</span>
                <span id="retry-text">Try Again</span>
            </button>
            
            <a href="/" class="btn btn-secondary">
                🏠 Go to Homepage
            </a>
        </div>

        <div class="connection-status">
            <div class="status-indicator">
                <div class="status-dot" id="status-dot"></div>
                <span id="status-text">Offline</span>
            </div>
        </div>

        <div class="offline-features">
            <h3>Available Offline</h3>
            <ul class="feature-list">
                <li>View cached pages and data</li>
                <li>Access previously loaded content</li>
                <li>Use basic app functionality</li>
                <li>Data will sync when back online</li>
            </ul>
        </div>
    </div>

    <script>
        // Connection monitoring
        function updateConnectionStatus() {
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            
            if (navigator.onLine) {
                statusDot.classList.add('online');
                statusText.textContent = 'Back Online!';
                
                // Auto-redirect after 2 seconds when back online
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusDot.classList.remove('online');
                statusText.textContent = 'Offline';
            }
        }

        // Retry connection
        function retryConnection() {
            const retryIcon = document.getElementById('retry-icon');
            const retryText = document.getElementById('retry-text');
            
            retryIcon.classList.add('retry-animation');
            retryText.textContent = 'Checking...';
            
            // Simulate checking connection
            setTimeout(() => {
                if (navigator.onLine) {
                    retryText.textContent = 'Connected!';
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    retryIcon.classList.remove('retry-animation');
                    retryText.textContent = 'Still Offline';
                    
                    setTimeout(() => {
                        retryText.textContent = 'Try Again';
                    }, 2000);
                }
            }, 1500);
        }

        // Event listeners
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'r' || e.key === 'R') {
                retryConnection();
            } else if (e.key === 'h' || e.key === 'H') {
                window.location.href = '/';
            }
        });

        // Service worker message handling
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data.type === 'CACHE_UPDATED') {
                    // Show update notification
                    const updateNotice = document.createElement('div');
                    updateNotice.innerHTML = `
                        <div style="position: fixed; top: 20px; right: 20px; background: rgba(16, 185, 129, 0.9); color: white; padding: 12px 16px; border-radius: 8px; font-size: 14px; z-index: 1000;">
                            ✅ New content available! <a href="#" onclick="window.location.reload()" style="color: white; text-decoration: underline;">Refresh</a>
                        </div>
                    `;
                    document.body.appendChild(updateNotice);
                    
                    setTimeout(() => {
                        updateNotice.remove();
                    }, 5000);
                }
            });
        }

        // Analytics for offline usage (if available)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'offline_page_view', {
                'event_category': 'PWA',
                'event_label': 'Offline Page Accessed'
            });
        }
    </script>
</body>
</html>
