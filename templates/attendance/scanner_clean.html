<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Attendance Scanner - Librainian</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    {% load static %}

    <style>
        :root{
            --border: #e2e8f0;
            --text: #1e293b;
            --muted: #64748b;
            --success: #10b981;
            --danger: #ef4444;
        }

        body{
            background: #fff;
            color: var(--text);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding-bottom: 80px; /* space for sticky logout */
        }

        .wrapper{
            width: 100%;
            max-width: 520px;
            margin: 0 auto;
            padding: 16px 16px 120px; /* extra for iOS safe area + logout */
        }

        .page-title{
            font-weight: 700;
            font-size: 1.5rem;
        }

        /* Soft shadow for cards */
        .card-soft-shadow {
            box-shadow: -4px 4px 10px rgba(0,0,0,0.05);
        }
        /* Time */
        .time-box{
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 12px 16px;
            margin-top: 12px;
            margin-bottom: 16px;
            text-align: center;
        }
        .clock{
            font-family: "Courier New", monospace;
            font-size: 1.25rem;   /* smaller */
            letter-spacing: 1px;
        }
        .date-line{
            font-size: .9rem;
            color: var(--muted);
        }

        /* Scanner card */
        .scanner-card {
            position: sticky;
            top: 12px;
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 16px;
            background: #f1f1f1;
            color: #000;
            background-clip: padding-box;
            position: relative;
        }
        .scanner-card::before {
            content: "";
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 18px;
            padding: 2px;
            background: linear-gradient(270deg, #3b82f6, #60a5fa, #3b82f6);
            background-size: 400% 400%;
            animation: borderSparkle 3s linear infinite;
            z-index: -1;
        }
        @keyframes borderSparkle {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Square box for reader */
        #qr-reader{
            width: 100%;
            aspect-ratio: 1 / 1;
            height: auto;
            border: 1px dashed var(--border);
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Start/Stop buttons */
        .play-btn{
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: var(--success);
            color: #fff;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: .5rem;
            border: none;
        }
        .btn-label{
            font-weight: 600;
            color: var(--success);
        }
        .btn-stop{
            background: #6b7280;
            border: none;
        }

        .scanner-status{
            font-size: .95rem;
            border-radius: 8px;
            padding: .75rem 1rem;
        }
        .status-info{
            background: rgba(59,130,246,.08);
            color: #2563eb;
        }
        .status-success{
            background: rgba(16,185,129,.08);
            color: #059669;
        }
        .status-error{
            background: rgba(239,68,68,.08);
            color: #dc2626;
        }

        /* Feedback flashes */
        body.success-flash{
            animation: successPulse 1.8s ease-in-out;
            background: #fff;
        }
        @keyframes successPulse{
            0%{ background:#fff; }
            50%{ background:#dcfce7; }
            100%{ background:#fff; }
        }
        body.error-flash{
            animation: errorPulse 1.8s ease-in-out;
            background: #fff;
        }
        @keyframes errorPulse{
            0%{ background:#fff; }
            50%{ background:#fee2e2; }
            100%{ background:#fff; }
        }

        /* Sticky logout at bottom */
        .logout-footer{
            position: fixed;
            left: 0; right: 0; bottom: 0;
            background: #fff;
            border-top: 1px solid var(--border);
            padding: 12px 16px calc(env(safe-area-inset-bottom, 0) + 12px);
            z-index: 999;
        }

        /* Hide html5-qrcode’s default dashboard/header */
        #qr-reader__dashboard_section,
        #qr-reader__header_message{
            display: none !important;
        }

        /* Loading overlay */
        .loading-overlay{
            position: fixed;
            inset: 0;
            background: rgba(255,255,255,.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .loading-spinner{
            width: 48px; height: 48px;
            border: 4px solid rgba(0,0,0,.08);
            border-top: 4px solid var(--success);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin{
            to{ transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Sounds -->
    <audio id="successAudio" preload="auto">
        <source src="{% static 'audio/success.wav' %}" type="audio/wav">
    </audio>
    <audio id="failAudio" preload="auto">
        <source src="{% static 'audio/fail.wav' %}" type="audio/wav">
    </audio>

    <!-- Loading -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <main class="wrapper">
        <header class="mb-2">
            <h1 class="page-title d-flex align-items-center gap-2">
                <i class="fas fa-qrcode"></i>
                Attendance Scanner
            </h1>
            <p class="text-muted mb-0">Scan student QR codes for attendance</p>
        </header>

        <!-- Time -->
        <section class="time-box card-soft-shadow">
            <div id="currentTime" class="clock"></div>
            <div id="currentDate" class="date-line"></div>
        </section>

        <!-- Scanner (sticky) -->
        <section id="scannerCard" class="scanner-card card-soft-shadow">
            <div id="qr-reader" class="mb-3">
                <!-- Centered start button before camera starts -->
                <div id="preStart" class="d-flex flex-column align-items-center justify-content-center h-100 w-100">
                    <button id="startBtn" class="play-btn" onclick="startScanner()">
                        <i class="fas fa-play"></i>
                    </button>
                    <span class="btn-label mt-2">Start Scanner</span>
                </div>
            </div>

            <div class="d-flex gap-2 justify-content-center mb-3">
                <button id="stopBtn" class="btn btn-stop text-white d-none" onclick="stopScanner()">
                    <i class="fas fa-stop me-1"></i> Stop
                </button>
            </div>

            <div id="scannerStatus" class="scanner-status status-info">
                <i class="fas fa-info-circle me-1"></i>
                Tap the Play button to start scanning
            </div>
            <p id="restartHint" class="text-center text-muted mt-2 d-none">Tap the Play button to restart scanner.</p>
        </section>

        <!-- Hidden Form -->
        <form id="scanForm" action="{% url 'attendance:scanner_result' %}" method="post" style="display:none;">
            {% csrf_token %}
            <input type="hidden" id="qrContent" name="qr_content">
        </form>
    </main>

    <!-- Sticky Logout -->
    <footer class="logout-footer">
        <div class="d-flex justify-content-center align-items-center">
            <a href="{% url 'attendance:scanner_logout' %}" class="text-danger fw-semibold text-decoration-none">
                <i class="fas fa-sign-out-alt me-2"></i> Logout
            </a>
        </div>
    </footer>

    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <script>
        let html5QrCode;
        let isScanning = false;

        function initializeScanner() {
            html5QrCode = new Html5Qrcode("qr-reader");
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function startScanner() {
            if (isScanning) return;

            // Hide preStart overlay
            const pre = document.getElementById('preStart');
            if (pre) pre.classList.add('d-none');

            html5QrCode.start(
                { facingMode: "environment" },
                { fps: 10, qrbox: { width: 250, height: 250 } },
                onScanSuccess,
                onScanFailure
            ).then(() => {
                isScanning = true;
                document.getElementById('stopBtn').classList.remove('d-none');
                showStatus('Scanner started. Point camera at QR code.', 'success');
                document.getElementById('restartHint').classList.add('d-none');
            }).catch(err => {
                console.error('Failed to start scanner:', err);
                let msg = 'Failed to start camera. ';
                if (err.name === 'NotAllowedError') msg += 'Please allow camera access and refresh.';
                else if (err.name === 'NotFoundError') msg += 'No camera found on this device.';
                else if (err.name === 'NotSupportedError') msg += 'Camera not supported on this browser.';
                else if (err.name === 'NotReadableError') msg += 'Camera is busy with another app.';
                else msg += 'Please check camera permissions and try again.';

                showStatus(msg, 'error');
                // If failed, show the start UI again
                const pre = document.getElementById('preStart');
                if (pre) pre.classList.remove('d-none');
            });
        }

        function stopScanner() {
            if (!isScanning) return;
            html5QrCode.stop().then(() => {
                isScanning = false;
                document.getElementById('stopBtn').classList.add('d-none');
                showStatus('Scanner stopped.', 'info');
                document.getElementById('restartHint').classList.remove('d-none');
                setTimeout(() => {
                    if (!isScanning) startScanner();
                }, 50000);

                // Re-show preStart overlay
                const pre = document.getElementById('preStart');
                if (pre) pre.classList.remove('d-none');
            }).catch(err => console.error('Failed to stop scanner:', err));
        }

        function onScanSuccess(decodedText, decodedResult) {
            stopScanner();
            showSuccessFeedback();

            setTimeout(() => {
                document.getElementById('qrContent').value = decodedText;
                document.getElementById('scanForm').submit();
            }, 400);
        }

        function onScanFailure(error) {
            // ignore
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('scannerStatus');
            statusDiv.className = 'scanner-status status-' + type;
            const icon = type === 'success' ? 'check-circle' :
                         type === 'error' ? 'exclamation-triangle' : 'info-circle';
            statusDiv.innerHTML = `<i class="fas fa-${icon} me-1"></i>${message}`;
        }

        function playSuccessSound(){
            const audio = document.getElementById('successAudio');
            audio.currentTime = 0;
            audio.play().catch(()=>{});
            setTimeout(()=>{ audio.pause(); audio.currentTime = 0; }, 2000);
        }
        function playFailSound(){
            const audio = document.getElementById('failAudio');
            audio.currentTime = 0;
            audio.play().catch(()=>{});
            setTimeout(()=>{ audio.pause(); audio.currentTime = 0; }, 2000);
        }

        function showSuccessFeedback(){
            document.body.classList.add('success-flash');
            playSuccessSound();
            setTimeout(()=> document.body.classList.remove('success-flash'), 1500);
        }
        function showErrorFeedback(){
            document.body.classList.add('error-flash');
            playFailSound();
            setTimeout(()=> document.body.classList.remove('error-flash'), 1500);
        }

        function updateTime(){
            const now = new Date();
            const timeStr = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            }); // hh:mm:ss AM
            const dateStr = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            document.getElementById('currentTime').textContent = timeStr;
            document.getElementById('currentDate').textContent = dateStr;
        }

        document.addEventListener('DOMContentLoaded', () => {
            // time
            updateTime();
            setInterval(updateTime, 1000);

            // audio preload
            document.getElementById('successAudio').load();
            document.getElementById('failAudio').load();

            initializeScanner();
        });

        // Pause camera if user switches tabs
        document.addEventListener('visibilitychange', function() {
            if (document.hidden && isScanning) stopScanner();
        });
    </script>
</body>
</html>