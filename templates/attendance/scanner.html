{% extends "base.html" %}
{% load static %}

{% block page_title %}Attendance Scanner{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'attendance:today_attendance' %}">Attendance</a></li>
<li class="breadcrumb-item active" aria-current="page">Scanner</li>
{% endblock %}

{% block content %}
<style>
  :root{
    --bg: #ffffff;
    --text: #111827;
    --muted: #6b7280;
    --accent: #2563eb;
    --success: #22c55e;
    --error: #ef4444;
    --warning: #d97706;
    --pink: #f472b6;
    --radius: 12px;
    --font: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  }

  *{ box-sizing: border-box; }

  .scanner-container{
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    min-height: 70vh;
    display: flex;
    flex-direction: column;
  }

  body.pass-bg{ background: #ecfdf5; }
  body.fail-bg{ background: #fef2f2; }

  .page{
    display:flex;
    flex-direction:column;
    flex: 1;
  }

  /* Time display */
  .time-display{
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius);
    padding: 15px;
    text-align: center;
    margin-bottom: 20px;
    box-shadow: var(--glass-shadow);
  }
  .time-display .time{
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text);
  }
  .time-display .date{
    font-size: 0.9rem;
    color: var(--muted);
    margin-top: 4px;
  }

  /* Scanner section */
  .scanner-section{
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
  }

  /* Scanner box */
  .scanner-box{
    position: relative;
    width: 100%;
    max-width: 400px;
    aspect-ratio: 1 / 1;
    border: 2px solid var(--accent);
    border-radius: var(--radius);
    overflow: hidden;
    background: #f8fafc;
    box-shadow: 0 6px 24px rgba(37,99,235,0.12);
  }

  #qr-reader{
    width:100%;
    height:100%;
  }

  /* Center control button */
  .control-btn{
    position:absolute;
    left:50%;
    top:50%;
    transform: translate(-50%,-50%);
    width: 84px;
    height: 84px;
    border-radius: 50%;
    background: var(--pink);
    display:flex;
    align-items:center;
    justify-content:center;
    border:none;
    cursor:pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  }

  .control-icon{
    font-size: 36px;
    line-height: 1;
    user-select: none;
  }

  .control-icon.stop{ color:#ef4444; }     /* ● */
  .control-icon.play{ color:#065f46; }     /* ▶ */
  .control-icon.square{ color:#16a34a; }   /* ■ */

  /* Status strip */
  .status{
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 12px;
    background: #f3f4f6;
    color: var(--muted);
    border: 1px solid #e5e7eb;
    border-radius: 999px;
    padding: 6px 14px;
    font-size: 0.85rem;
    display:none;
  }
  .status.show{ display:block; }
  .status.success{
    background: #dcfce7;
    border-color:#bbf7d0;
    color:#166534;
  }
  .status.error{
    background:#fee2e2;
    border-color:#fecaca;
    color:#b91c1c;
  }
  .status.warning{
    background:#fffbeb;
    border-color:#fed7aa;
    color:#92400e;
  }

  /* Loading overlay inside scanner */
  .loading{
    position:absolute;
    inset:0;
    background: rgba(255,255,255,0.92);
    display:flex;
    flex-direction:column;
    align-items:center;
    justify-content:center;
    gap:8px;
    font-size:0.9rem;
  }
  .spinner{
    width:28px;height:28px;border-radius:50%;
    border:3px solid #e5e7eb;border-top-color: var(--accent);
    animation: spin 1s linear infinite;
  }
  @keyframes spin{ to{ transform: rotate(360deg); } }
</style>

<div class="scanner-container">
  <!-- Time Display -->
  <div class="time-display">
    <div id="liveTime" class="time">--:--:-- --</div>
    <div id="liveDate" class="date">Loading date...</div>
  </div>

  <!-- Scanner Section -->
  <div class="scanner-section">
    <div class="scanner-box" id="scannerBox">
      <div id="qr-reader"></div>

      <!-- Loading state -->
      <div class="loading" id="loadingOverlay">
        <div class="spinner"></div>
        <div>Initializing camera...</div>
      </div>

      <!-- Central control -->
      <button class="control-btn" id="controlBtn" aria-label="Stop scanner">
        <span id="controlIcon" class="control-icon stop">●</span>
      </button>
    </div>

    <!-- Status -->
    <div id="scannerStatus" class="status"></div>
  </div>
</div>

<!-- Hidden form for submitting scan results -->
<form id="scanForm" method="POST" action="{% url 'attendance:scanner_result' %}" style="display:none;">
  {% csrf_token %}
  <input type="hidden" name="qr_content" id="qrContent">
</form>

<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script>
  let html5QrCode;
  let isScanning = false;
  let successFlashTimeout, statusHideTimeout, bgTimeout;

  function $(id){ return document.getElementById(id); }

  function updateClock(){
    const now = new Date();
    let hours = now.getHours();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12 || 12;
    const pad = n => String(n).padStart(2,'0');
    const timeStr = `${pad(hours)}:${pad(now.getMinutes())}:${pad(now.getSeconds())} ${ampm}`;
    $('liveTime').textContent = timeStr;

    const options = { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' };
    $('liveDate').textContent = now.toLocaleDateString(undefined, options);
  }

  function initClock(){
    updateClock();
    setInterval(updateClock, 1000);
  }

  function flashBackground(cls){
    document.body.classList.remove('pass-bg','fail-bg');
    void document.body.offsetWidth; // reflow to restart transition
    document.body.classList.add(cls);
    clearTimeout(bgTimeout);
    bgTimeout = setTimeout(() => {
      document.body.classList.remove(cls);
    }, 800);
  }

  function setStatus(msg, type=''){
    const el = $('scannerStatus');
    el.textContent = msg;
    el.className = 'status show' + (type ? ' ' + type : '');
    clearTimeout(statusHideTimeout);
    statusHideTimeout = setTimeout(()=> {
      el.classList.remove('show');
    }, 3000);
  }

  function setControl(state){
    const icon = $('controlIcon');
    if(state === 'running'){
      icon.textContent = '●';
      icon.className = 'control-icon stop';
      $('controlBtn').setAttribute('aria-label', 'Stop scanner');
    }else if(state === 'stopped'){
      icon.textContent = '▶';
      icon.className = 'control-icon play';
      $('controlBtn').setAttribute('aria-label', 'Start scanner');
    }else if(state === 'success'){
      icon.textContent = '■';
      icon.className = 'control-icon square';
      $('controlBtn').setAttribute('aria-label', 'Success');
      clearTimeout(successFlashTimeout);
      successFlashTimeout = setTimeout(()=> {
        setControl('stopped');
      }, 1000);
    }
  }

  function initializeScanner(){
    html5QrCode = new Html5Qrcode("qr-reader");
    $('loadingOverlay').style.display = 'none';
  }

  function startScanner(){
    if(isScanning) return;

    const config = {
      fps: 10,
      qrbox: { width: 250, height: 250 },
      aspectRatio: 1.0
    };

    html5QrCode.start(
      { facingMode: "environment" },
      config,
      onScanSuccess,
      onScanFailure
    ).then(()=>{
      isScanning = true;
      setControl('running');
      setStatus('Scanner started. Point the camera at a QR code.','success');
    }).catch(err=>{
      let msg = 'Failed to start camera.';
      if (err?.name === 'NotAllowedError') {
        msg = 'Camera permission denied.';
      } else if (err?.name === 'NotFoundError') {
        msg = 'No camera found.';
      } else if (err?.name === 'NotReadableError') {
        msg = 'Camera is busy.';
      }
      setStatus(msg, 'error');
      flashBackground('fail-bg');
      setControl('stopped');
    });
  }

  function stopScanner(){
    if(!isScanning) return;
    html5QrCode.stop().then(()=>{
      isScanning = false;
      setControl('stopped');
      setStatus('Scanner stopped.','warning');
    }).catch(()=>{
      setStatus('Unable to stop scanner','error');
    });
  }

  function toggleScanner(){
    if(isScanning){
      stopScanner();
    }else{
      startScanner();
    }
  }

  function onScanSuccess(decodedText){
    stopScanner();
    setControl('success');
    flashBackground('pass-bg');
    setStatus('Scan successful. Submitting...','success');

    $('qrContent').value = decodedText;
    $('scanForm').submit();
  }

  function onScanFailure() {}

  document.addEventListener('DOMContentLoaded', () => {
    initClock();
    initializeScanner();
    setControl('stopped');
    setTimeout(startScanner, 400);
    $('controlBtn').addEventListener('click', toggleScanner);
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && isScanning) stopScanner();
    });
  });
</script>
{% endblock %}