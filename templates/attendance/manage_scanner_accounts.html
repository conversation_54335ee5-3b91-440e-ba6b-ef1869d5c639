{% extends "base.html" %}
{% load static %}

{% block page_title %}Manage Scanner Accounts{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/{{ role }}/dashboard/">Home</a></li>
<li class="breadcrumb-item"><a href="{% url 'attendance:today_attendance' %}">Attendance</a></li>
<li class="breadcrumb-item active" aria-current="page">Scanner Accounts</li>
{% endblock %}

{% block content %}
<style>
    .scanner-accounts-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .header-card, .create-account-card, .accounts-list-card {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }
    .header-card {
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
    }
    .create-account-card, .accounts-list-card {
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
    }
    .accounts-list-card {
        margin-bottom: 0;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        color: #374151;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: 10px;
        background: var(--bg-primary);
        color: var(--text-primary);
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    .btn-create {
        background: #059669;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 10px 20px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
    }

    .scanner-account-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border: 1px solid #e5e7eb;
        border-radius: 10px;
        margin-bottom: 10px;
        background: #fff;
        transition: background 0.2s;
    }

    .account-info .account-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 5px;
    }

    .account-info .account-details {
        font-size: 0.85rem;
        color: var(--text-secondary);
    }

    .account-actions {
        display: flex;
        gap: 10px;
    }

    .btn-delete {
        background: #dc2626;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 0.8rem;
        cursor: pointer;
    }

    .alert {
        padding: 12px 16px;
        border-radius: 10px;
        margin-bottom: 20px;
        font-size: 0.9rem;
    }

    .alert-success {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.3);
        color: #059669;
    }

    .alert-danger {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: #dc2626;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        color: #6b7280;
    }

    .scanner-badge {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .scanner-account-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .account-actions {
            width: 100%;
            justify-content: flex-end;
        }
    }
</style>

<div class="scanner-accounts-container">
    <!-- Header -->
    <div class="header-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    Scanner Account Management
                </h1>
                <p class="mb-0 opacity-90">
                    Create and manage scanner-only accounts for {{ librarian.library_name }}
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="scanner-badge">
                    Scanner System
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div id="messageContainer"></div>

    <!-- Create New Account -->
    <div class="create-account-card">
        <h3 class="mb-4">
            Create New Scanner Account
        </h3>
        
        <form id="createAccountForm">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="username" class="form-label">
                            Username
                        </label>
                        <input type="text" 
                               id="username" 
                               name="username" 
                               class="form-control" 
                               placeholder="scanner01"
                               required
                               pattern="[a-zA-Z0-9._-]+"
                               title="Only letters, numbers, periods, hyphens, and underscores allowed">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="password" class="form-label">
                            Password
                        </label>
                        <input type="password" 
                               id="password" 
                               name="password" 
                               class="form-control" 
                               placeholder="Enter secure password"
                               required
                               minlength="6">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="full_name" class="form-label">
                            Full Name
                        </label>
                        <input type="text" 
                               id="full_name" 
                               name="full_name" 
                               class="form-control" 
                               placeholder="Scanner Operator Name"
                               required>
                    </div>
                </div>
            </div>
            
            <div class="text-end">
                <button type="submit" class="btn-create" id="createBtn">
                    Create Scanner Account
                </button>
            </div>
        </form>
    </div>

    <!-- Existing Accounts -->
    <div class="accounts-list-card">
        <h3 class="mb-4">
            Existing Scanner Accounts ({{ scanner_users.count }})
        </h3>
        
        <div id="accountsList">
            {% if scanner_users %}
                {% for user in scanner_users %}
                <div class="scanner-account-item" data-user-id="{{ user.id }}">
                    <div class="account-info">
                        <div class="account-name">{{ user.get_full_name|default:user.username }}</div>
                        <div class="account-details">
                            Username: {{ user.username }} |
                            {% if user.scanner_profile %}Library: {{ user.scanner_profile.librarian.library_name }} | {% endif %}
                            Created: {{ user.date_joined|date:"M d, Y" }} |
                            Last Login: {{ user.last_login|date:"M d, Y H:i"|default:"Never" }}
                        </div>
                    </div>
                    <div class="account-actions">
                        <button class="btn-delete" onclick="deleteAccount({{ user.id }}, '{{ user.username }}')">
                            Delete
                        </button>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <h4>No Scanner Accounts</h4>
                    <p>Create your first scanner-only account using the form above.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Create account form handler
document.getElementById('createAccountForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        username: formData.get('username'),
        password: formData.get('password'),
        full_name: formData.get('full_name')
    };
    
    const createBtn = document.getElementById('createBtn');
    createBtn.disabled = true;
    createBtn.innerHTML = 'Creating...';
    
    fetch('/attendance/api/create-scanner-account/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            this.reset();
            location.reload(); // Refresh to show new account
        } else {
            showMessage('Error: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error creating account', 'danger');
    })
    .finally(() => {
        createBtn.disabled = false;
        createBtn.innerHTML = 'Create Scanner Account';
    });
});

// Delete account function
function deleteAccount(userId, username) {
    if (!confirm(`Are you sure you want to delete scanner account "${username}"?\n\nThis action cannot be undone.`)) {
        return;
    }
    
    fetch('/attendance/api/delete-scanner-account/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({ user_id: userId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Remove the account item from the list
            const accountItem = document.querySelector(`[data-user-id="${userId}"]`);
            if (accountItem) {
                accountItem.remove();
            }
            
            // Check if list is empty
            const accountsList = document.getElementById('accountsList');
            if (accountsList.children.length === 0) {
                accountsList.innerHTML = `
                    <div class="empty-state">
                        <h4>No Scanner Accounts</h4>
                        <p>Create your first scanner-only account using the form above.</p>
                    </div>
                `;
            }
        } else {
            showMessage('Error: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error deleting account', 'danger');
    });
}

// Show message function
function showMessage(message, type) {
    const messageContainer = document.getElementById('messageContainer');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `${message}`;
    
    messageContainer.innerHTML = '';
    messageContainer.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
    
    // Scroll to top to show message
    window.scrollTo({ top: 0, behavior: 'smooth' });
}
</script>
{% endblock %}
