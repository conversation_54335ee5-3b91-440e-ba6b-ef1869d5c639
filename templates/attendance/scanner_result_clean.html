<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sign In - Librainian</title>
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@400;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --green: #16a34a;
      --text: #111827;
      --muted: #6b7280;
      --radius: 8px;
      --font: 'Comfortaa', cursive;
    }
    * { box-sizing: border-box; margin: 0; padding: 0; }
    body {
      font-family: var(--font);
      background: #ffffff;
      color: var(--text);
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      padding: 16px;
      text-align: center;
    }

    body.bg-green { background: #f0fdf4; }
    body.bg-blue { background: #eff6ff; }
    body.bg-orange { background: #fffbeb; }
    body.bg-red { background: #fef2f2; }
    .content {
      flex: 1 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 12px;
    }
    .welcome {
      font-size: 1rem;
      font-weight: 600;
    }
    .sign-in-status {
      font-size: 2rem;
      font-weight: 700;
      color: var(--green);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    .sign-in-status .tick {
      font-size: 1.5em;
    }
    .attendance-title {
      font-size: 3rem;
      font-weight: 700;
      color: var(--text);
    }
    .name, .time {
      font-size: 0.75rem;
      color: var(--muted);
    }
    .actions {
      margin-top: 16px;
      width: 100%;
    }
    .btn {
      display: block;
      width: 100%;
      padding: 0.5rem;
      border: none;
      border-radius: var(--radius);
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      text-decoration: none;
    }
    .btn-scan {
      background: var(--green);
      color: #fff;
    }
    footer {
      flex-shrink: 0;
      background: #fff;
      border-top: 1px solid #e5e7eb;
      padding: 8px;
      text-align: center;
      position: sticky;
      bottom: 0;
    }
    .info {
      font-size: 0.85rem;
      color: var(--muted);
      margin-bottom: 4px;
    }
    .logout-link {
      color: #dc2626;
      font-weight: 600;
      text-decoration: none;
    }
  </style>
</head>
<body class="bg-{{ background_color }}">
  <div class="content">
    {% if status == 'success' %}
        {% if background_color == 'green' %}
            <div class="welcome">Welcome!</div>
            <div class="sign-in-status">
                <span class="tick">✔</span> Sign In Recorded
            </div>
        {% elif background_color == 'blue' %}
            <div class="welcome">Goodbye!</div>
            <div class="sign-in-status" style="color: #3b82f6;">
                <span class="tick">✔</span> Sign Out Recorded
            </div>
        {% endif %}
    {% elif status == 'warning' %}
        <div class="welcome">Notice</div>
        <div class="sign-in-status" style="color: #f59e0b;">
            <span class="tick">⚠</span> {{ message }}
        </div>
    {% elif status == 'error' %}
        <div class="welcome">Error</div>
        <div class="sign-in-status" style="color: #ef4444;">
            <span class="tick">✗</span> {{ message }}
        </div>
    {% endif %}

    <div class="attendance-title">{{ message }}</div>
    {% if student %}
    <div class="name">Name: {{ student.name }}</div>
    {% endif %}
    {% if attendance %}
    <div class="time">Time: {{ attendance.scan_datetime|date:"H:i:s" }}</div>
    {% endif %}
    <div class="actions">
      <a href="{% url 'attendance:scanner_page' %}" class="btn btn-scan">Scan Another Account</a>
    </div>
  </div>
  <footer>
    <div class="info">
      Automatically returning to scanner in <span id="countdown">5</span> seconds...
    </div>
    <a href="{% url 'attendance:scanner_logout' %}" class="logout-link">Logout</a>
  </footer>
  <script>
    let countdown = 5;
    const countdownElement = document.getElementById('countdown');
    const timer = setInterval(() => {
      countdown--;
      countdownElement.textContent = countdown;
      if (countdown <= 0) {
        clearInterval(timer);
        window.location.href = "{% url 'attendance:scanner_page' %}";
      }
    }, 1000);
  </script>
</body>
</html>
