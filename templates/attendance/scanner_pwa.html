{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Scanner - Librainian</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#10b981">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Scanner">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-TileColor" content="#10b981">
    <meta name="msapplication-tap-highlight" content="no">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{% static 'js/scanner-manifest.json' %}">

    <!-- Icons -->
    <link rel="icon" href="{% static 'favicon.ico' %}">
    <link rel="apple-touch-icon" href="{% static 'img/apple-touch-icon.png' %}">
    
    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    
    <!-- PWA Styles -->
    <link rel="stylesheet" href="{% static 'css/pwa-styles.css' %}">
    
    <style>
        :root {
            --scanner-primary: #10b981;
            --scanner-secondary: #059669;
            --scanner-success: #22c55e;
            --scanner-error: #ef4444;
            --scanner-warning: #f59e0b;
        }

        body {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .scanner-pwa-container {
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .scanner-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .scanner-header h1 {
            color: var(--scanner-primary);
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 28px;
        }

        .scanner-header p {
            color: #64748b;
            font-size: 16px;
            margin: 0;
        }

        .scanner-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .scanner-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .qr-reader-container {
            position: relative;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            border-radius: 15px;
            overflow: hidden;
            background: #000;
        }

        #qr-reader {
            width: 100%;
            border-radius: 15px;
        }

        .scanner-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .scanner-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 120px;
            justify-content: center;
        }

        .scanner-btn-primary {
            background: linear-gradient(135deg, var(--scanner-primary), var(--scanner-secondary));
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .scanner-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
            color: white;
        }

        .scanner-btn-secondary {
            background: rgba(255, 255, 255, 0.8);
            color: var(--scanner-primary);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .scanner-btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            color: var(--scanner-primary);
        }

        .scanner-status {
            text-align: center;
            padding: 15px;
            border-radius: 12px;
            margin-top: 20px;
            font-weight: 500;
            display: none;
        }

        .scanner-status.success {
            background: rgba(34, 197, 94, 0.1);
            color: var(--scanner-success);
            border: 1px solid rgba(34, 197, 94, 0.3);
            display: block;
        }

        .scanner-status.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--scanner-error);
            border: 1px solid rgba(239, 68, 68, 0.3);
            display: block;
        }

        .scanner-status.warning {
            background: rgba(245, 158, 11, 0.1);
            color: var(--scanner-warning);
            border: 1px solid rgba(245, 158, 11, 0.3);
            display: block;
        }

        .scanner-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--scanner-primary);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .scanner-footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 15px;
        }

        .scanner-footer p {
            margin: 0;
            color: #64748b;
            font-size: 14px;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .scanner-pwa-container {
                padding: 15px;
            }

            .scanner-header {
                padding: 15px;
                margin-bottom: 20px;
            }

            .scanner-header h1 {
                font-size: 24px;
            }

            .scanner-card {
                padding: 20px;
            }

            .scanner-controls {
                flex-direction: column;
                align-items: center;
            }

            .scanner-btn {
                width: 100%;
                max-width: 200px;
            }

            .scanner-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            }

            .scanner-header,
            .scanner-card {
                background: rgba(17, 24, 39, 0.9);
                color: #f9fafb;
            }

            .scanner-header h1 {
                color: var(--scanner-success);
            }

            .scanner-header p {
                color: #d1d5db;
            }

            .stat-card {
                background: rgba(31, 41, 55, 0.6);
                color: #f9fafb;
            }

            .scanner-footer {
                background: rgba(31, 41, 55, 0.6);
            }

            .scanner-footer p {
                color: #d1d5db;
            }
        }

        /* Fullscreen mode optimizations */
        @media (display-mode: standalone) {
            .scanner-pwa-container {
                padding-top: 40px; /* Account for status bar */
            }
        }

        /* Loading animation */
        .scanner-loading {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .scanner-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="scanner-pwa-container">
        <!-- Header -->
        <div class="scanner-header">
            <h1><i class="fas fa-qrcode"></i> QR Scanner</h1>
            <p>Scan student QR codes for attendance</p>
        </div>

        <!-- Main Scanner -->
        <div class="scanner-main">
            <div class="scanner-card">
                <div class="qr-reader-container">
                    <div id="qr-reader"></div>
                    
                    <!-- Pre-start overlay -->
                    <div id="preStart" class="d-flex flex-column align-items-center justify-content-center position-absolute top-0 start-0 w-100 h-100" style="background: rgba(0,0,0,0.8); color: white;">
                        <button id="startBtn" class="scanner-btn scanner-btn-primary" onclick="startScanner()">
                            <i class="fas fa-play"></i>
                            Start Scanner
                        </button>
                    </div>
                </div>

                <div class="scanner-controls">
                    <button id="stopBtn" class="scanner-btn scanner-btn-secondary d-none" onclick="stopScanner()">
                        <i class="fas fa-stop"></i>
                        Stop
                    </button>
                    <button class="scanner-btn scanner-btn-secondary" onclick="toggleFlashlight()">
                        <i class="fas fa-flashlight" id="flashIcon"></i>
                        Flash
                    </button>
                </div>

                <div id="scannerStatus" class="scanner-status"></div>

                <!-- Scanner Stats -->
                <div class="scanner-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="todayScans">0</div>
                        <div class="stat-label">Today</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successfulScans">0</div>
                        <div class="stat-label">Success</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="failedScans">0</div>
                        <div class="stat-label">Failed</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="scanner-footer">
            <p>
                <i class="fas fa-shield-alt"></i>
                Secure attendance tracking powered by Librainian
            </p>
        </div>
    </div>

    <!-- Hidden form for submission -->
    <form id="scanForm" method="POST" action="{% url 'attendance:scanner_result' %}" style="display: none;">
        {% csrf_token %}
        <input type="hidden" id="qrContent" name="qr_content">
    </form>

    <!-- Audio feedback -->
    <audio id="successAudio" preload="auto">
        <source src="{% static 'audio/success.wav' %}" type="audio/wav">
    </audio>
    <audio id="failAudio" preload="auto">
        <source src="{% static 'audio/fail.wav' %}" type="audio/wav">
    </audio>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <script src="{% static 'js/pwa-manager.js' %}"></script>
    
    <script>
        let html5QrCode;
        let isScanning = false;
        let flashlightOn = false;
        let stats = {
            today: 0,
            successful: 0,
            failed: 0
        };

        // Initialize scanner when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeScanner();
            loadStats();
            
            // Register scanner-specific service worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/static/js/service_worker.js', {
                    scope: '/attendance/'
                }).then(registration => {
                    console.log('[Scanner PWA] Service Worker registered');
                }).catch(error => {
                    console.error('[Scanner PWA] Service Worker registration failed:', error);
                });
            }
        });

        function initializeScanner() {
            html5QrCode = new Html5Qrcode("qr-reader");
            console.log('[Scanner] Initialized');
        }

        function startScanner() {
            if (isScanning) return;

            const preStart = document.getElementById('preStart');
            if (preStart) preStart.classList.add('d-none');

            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0,
                supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA]
            };

            html5QrCode.start(
                { facingMode: "environment" },
                config,
                onScanSuccess,
                onScanFailure
            ).then(() => {
                isScanning = true;
                document.getElementById('stopBtn').classList.remove('d-none');
                showStatus('Scanner started. Point camera at QR code.', 'success');
            }).catch(err => {
                console.error('Failed to start scanner:', err);
                let msg = 'Failed to start camera. ';
                if (err.name === 'NotAllowedError') {
                    msg += 'Please allow camera access and refresh.';
                } else if (err.name === 'NotFoundError') {
                    msg += 'No camera found on this device.';
                } else if (err.name === 'NotSupportedError') {
                    msg += 'Camera not supported on this browser.';
                } else {
                    msg += 'Please check camera permissions.';
                }
                showStatus(msg, 'error');
            });
        }

        function stopScanner() {
            if (!isScanning) return;
            
            html5QrCode.stop().then(() => {
                isScanning = false;
                document.getElementById('stopBtn').classList.add('d-none');
                showStatus('Scanner stopped.', 'warning');
                
                const preStart = document.getElementById('preStart');
                if (preStart) preStart.classList.remove('d-none');
            }).catch(err => {
                console.error('Failed to stop scanner:', err);
                showStatus('Unable to stop scanner', 'error');
            });
        }

        function onScanSuccess(decodedText, decodedResult) {
            console.log('[Scanner] QR Code detected:', decodedText);
            
            // Stop scanner temporarily
            stopScanner();
            
            // Show success feedback
            showStatus('QR Code scanned successfully! Processing...', 'success');
            playSuccessSound();
            
            // Update stats
            stats.successful++;
            stats.today++;
            updateStatsDisplay();
            
            // Submit form
            setTimeout(() => {
                document.getElementById('qrContent').value = decodedText;
                document.getElementById('scanForm').submit();
            }, 1000);
        }

        function onScanFailure(error) {
            // Don't log every scan failure as it's normal
            // console.log('[Scanner] Scan failed:', error);
        }

        function toggleFlashlight() {
            // Note: Flashlight control is limited in web browsers
            // This is a placeholder for future implementation
            flashlightOn = !flashlightOn;
            const flashIcon = document.getElementById('flashIcon');
            
            if (flashlightOn) {
                flashIcon.classList.remove('fa-flashlight');
                flashIcon.classList.add('fa-lightbulb');
                showStatus('Flashlight turned on', 'success');
            } else {
                flashIcon.classList.remove('fa-lightbulb');
                flashIcon.classList.add('fa-flashlight');
                showStatus('Flashlight turned off', 'warning');
            }
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('scannerStatus');
            statusElement.textContent = message;
            statusElement.className = `scanner-status ${type}`;
            
            // Auto-hide after 3 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    statusElement.style.display = 'none';
                }, 3000);
            }
        }

        function playSuccessSound() {
            const audio = document.getElementById('successAudio');
            if (audio) {
                audio.play().catch(e => console.log('Audio play failed:', e));
            }
        }

        function playFailSound() {
            const audio = document.getElementById('failAudio');
            if (audio) {
                audio.play().catch(e => console.log('Audio play failed:', e));
            }
        }

        function loadStats() {
            // Load stats from localStorage or API
            const savedStats = localStorage.getItem('scannerStats');
            if (savedStats) {
                stats = JSON.parse(savedStats);
                updateStatsDisplay();
            }
        }

        function updateStatsDisplay() {
            document.getElementById('todayScans').textContent = stats.today;
            document.getElementById('successfulScans').textContent = stats.successful;
            document.getElementById('failedScans').textContent = stats.failed;
            
            // Save to localStorage
            localStorage.setItem('scannerStats', JSON.stringify(stats));
        }

        // Handle visibility change for performance
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && isScanning) {
                stopScanner();
            }
        });

        // Handle beforeunload
        window.addEventListener('beforeunload', () => {
            if (isScanning) {
                stopScanner();
            }
        });
    </script>
</body>
</html>
