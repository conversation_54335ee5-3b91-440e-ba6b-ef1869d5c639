<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scanner Access - Librainian</title>
    {% load static %}
    
    <style>
      .logo {
        display: block;
        margin: 0 auto 16px auto;
        max-width: 360px;
        height: auto;
      }
      :root {
        --bg: #ffffff;
        --text: #111827;
        --muted: #6b7280;
        --accent: #2563eb;
        --border: #d1d5db;
        --radius: 6px;
        --font: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      * { box-sizing: border-box; margin: 0; padding: 0; }
      body {
        font-family: var(--font);
        background: var(--bg);
        color: var(--text);
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
      }
      .login-container {
        width: 100%;
        max-width: 360px;
      }
      h1 {
        font-size: 1.5rem;
        text-align: center;
        margin-bottom: 8px;
      }
      p.subtitle {
        text-align: center;
        color: var(--muted);
        font-size: 0.9rem;
        margin-bottom: 24px;
      }
      form {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
      label {
        font-size: 0.9rem;
        margin-bottom: 4px;
        display: block;
      }
      input {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border);
        border-radius: var(--radius);
        font-size: 1rem;
      }
      input:focus {
        outline: none;
        border-color: var(--accent);
        box-shadow: 0 0 0 2px rgba(37,99,235,0.2);
      }
      button {
        padding: 12px;
        background: var(--accent);
        color: #fff;
        border: none;
        border-radius: var(--radius);
        font-size: 1rem;
        cursor: pointer;
      }
      button:hover {
        background: #1d4ed8;
      }
      .alert {
        background: #fee2e2;
        color: #b91c1c;
        padding: 10px;
        border: 1px solid #fecaca;
        border-radius: var(--radius);
        margin-bottom: 16px;
        font-size: 0.85rem;
      }
      .security-note {
        margin-top: 20px;
        font-size: 0.8rem;
        color: var(--muted);
        text-align: center;
        line-height: 1.4;
      }
    </style>
</head>
<body>
  <div class="login-container">
    <img src="{% static 'img/logo_trans_name.png' %}" alt="Librainian Logo" class="logo">
    <h1>Scanner Access</h1>
    <p class="subtitle">Secure attendance scanning system</p>
    {% if messages %}
      {% for message in messages %}
        <div class="alert">{{ message }}</div>
      {% endfor %}
    {% endif %}
    <form method="POST">
      {% csrf_token %}
      <div>
        <label for="username">Username</label>
        <input type="text" id="username" name="username" required autocomplete="username">
      </div>
      <div>
        <label for="password">Password</label>
        <input type="password" id="password" name="password" required autocomplete="current-password">
      </div>
      <button type="submit">Access Scanner</button>
    </form>
    <div class="security-note">
      <strong>Secure Access:</strong> This login provides access to attendance scanning functionality only. All activities are logged for security purposes.
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      document.getElementById('username').focus();
    });
  </script>
</body>
</html>
