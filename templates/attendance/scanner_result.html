<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Result</title>
    <style>
      :root {
        --bg-success: #ecfdf5;
        --bg-error: #fef2f2;
        --bg-warning: #fffbeb;
        --text: #111827;
        --muted: #6b7280;
        --accent: #2563eb;
        --radius: 12px;
        --font: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      * { box-sizing: border-box; margin:0; padding:0; }
      body {
        margin: 0;
        padding: 0;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: var(--font);
        background: #fff;
        transition: background-color 0.5s ease;
      }
      body.bg-success { background: var(--bg-success); }
      body.bg-error { background: var(--bg-error); }
      body.bg-warning { background: var(--bg-warning); }
      .result-container {
        text-align: center;
        padding: 24px;
        border-radius: var(--radius);
        background: white;
        max-width: 480px;
        width: 92%;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        animation: fadeIn 0.4s ease;
      }
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .result-icon {
        font-size: 3rem;
        margin-bottom: 16px;
      }
      .result-title {
        font-size: 1.6rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: var(--text);
      }
      .result-message {
        font-size: 1rem;
        color: var(--muted);
        margin-bottom: 20px;
      }
      .student-info {
        border: 1px solid #e5e7eb;
        border-radius: var(--radius);
        padding: 16px;
        margin-bottom: 20px;
        text-align: left;
        font-size: 0.9rem;
        color: var(--text);
      }
      .student-name {
        font-weight: 600;
        margin-bottom: 4px;
      }
      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
        margin-bottom: 10px;
      }
      .btn-action {
        padding: 10px 18px;
        border: none;
        border-radius: var(--radius);
        text-decoration: none;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        color: #fff;
      }
      .btn-primary-action {
        background: var(--accent);
      }
      .btn-secondary-action {
        background: var(--muted);
      }
      .auto-redirect {
        margin-top: 12px;
        color: var(--muted);
        font-size: 0.85rem;
      }
      .countdown {
        font-weight: 600;
        color: var(--text);
      }
      @media(max-width: 600px) {
        .result-title { font-size: 1.3rem; }
        .result-icon { font-size: 2.5rem; }
        .btn-action { flex:1 1 100%; text-align:center; }
      }
    </style>
</head>
<body class="bg-{{ background_color }}">
    <div class="result-container">
        <!-- Status Icon -->
        <div class="result-icon">
            {% if status == 'success' %}✔{% elif status == 'error' %}✖{% elif status == 'warning' %}⚠{% else %}ℹ{% endif %}
        </div>

        <!-- Result Title -->
        <h1 class="result-title">
            {% if status == 'success' %}
                Attendance Marked!
            {% elif status == 'expired' %}
                QR Code Expired
            {% elif status == 'error' %}
                Scan Failed
            {% else %}
                Attendance Status
            {% endif %}
        </h1>

        <!-- Result Message -->
        <p class="result-message">{{ message }}</p>

        <!-- Student Information -->
        {% if student %}
        <div class="student-info">
            <div class="student-name">
                {{ student.name }}
            </div>
            <div class="student-details">
                <div><strong>ID:</strong> {{ student.unique_id }}</div>
                <div><strong>Email:</strong> {{ student.email }}</div>
                {% if attendance %}
                <div><strong>Time:</strong> {{ attendance.scan_datetime|date:"H:i:s" }}</div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{% url 'attendance:scanner_page' %}" class="btn-action btn-primary-action">Scan Another</a>
            <a href="{% url 'attendance:today_attendance' %}" class="btn-action btn-secondary-action">View Today's List</a>
        </div>

        <!-- Auto-redirect countdown -->
        <div class="auto-redirect">
            Redirecting to scanner in <span class="countdown" id="countdown">5</span> seconds
        </div>
    </div>

    <!-- Audio elements -->
    {% if status == 'success' %}
    <audio id="resultAudio" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    {% elif status == 'error' or status == 'expired' %}
    <audio id="resultAudio" preload="auto">
        <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
    </audio>
    {% endif %}

    <script>
        // Enhanced audio feedback with fallback
        document.addEventListener('DOMContentLoaded', function() {
            const audio = document.getElementById('resultAudio');
            if (audio) {
                audio.play().catch(e => {
                    console.log('Audio play failed:', e);
                    // Fallback: create beep sound based on status
                    try {
                        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        const oscillator = audioContext.createOscillator();
                        const gainNode = audioContext.createGain();

                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        // Different frequencies for different statuses
                        const status = '{{ status }}';
                        if (status === 'success') {
                            oscillator.frequency.value = 800; // High pitch for success
                        } else if (status === 'warning') {
                            oscillator.frequency.value = 600; // Medium pitch for warning
                        } else {
                            oscillator.frequency.value = 400; // Low pitch for error
                        }

                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.5);
                    } catch (fallbackError) {
                        console.log('Fallback audio also failed:', fallbackError);
                    }
                });
            }
        });

        // Auto-redirect countdown
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '{% url "attendance:scanner_page" %}';
            }
        }, 1000);

        // Allow user to cancel auto-redirect by clicking anywhere
        document.addEventListener('click', function() {
            clearInterval(timer);
            countdownElement.parentElement.style.display = 'none';
        });
    </script>
</body>
</html>
