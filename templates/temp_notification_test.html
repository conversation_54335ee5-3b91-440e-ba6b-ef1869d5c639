{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temporary Notification Tester - Librainian</title>
    
    <!-- Bootstrap 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    
    <!-- PWA Styles -->
    <link rel="stylesheet" href="{% static 'css/pwa-styles.css' %}">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }

        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .warning-banner {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .test-section {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .btn-test {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-danger-test {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .btn-danger-test:hover {
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        }

        .status-log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .notification-preview {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-right: 15px;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .notification-body {
            color: #6b7280;
            font-size: 14px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Warning Banner -->
        <div class="warning-banner">
            <i class="fas fa-exclamation-triangle"></i>
            TEMPORARY TESTING FUNCTION - REMEMBER TO DELETE AFTER TESTING!
            <i class="fas fa-exclamation-triangle"></i>
        </div>

        <!-- Header -->
        <div class="text-center mb-4">
            <h1><i class="fas fa-bell"></i> PWA Notification Tester</h1>
            <p class="text-muted">Test PWA notifications with automatic periodic sending</p>
        </div>

        <!-- Single Test Section -->
        <div class="test-section">
            <h3><i class="fas fa-paper-plane"></i> Single Test Notification</h3>
            <p>Send a single test notification immediately to all registered devices.</p>
            <button class="btn btn-test" onclick="sendSingleTest()">
                <i class="fas fa-rocket"></i> Send Single Test
            </button>
        </div>

        <!-- Periodic Test Section -->
        <div class="test-section">
            <h3><i class="fas fa-clock"></i> Periodic Notification Sender</h3>
            <p>Start sending notifications every minute with increasing numbers. <strong>This runs in the background!</strong></p>
            <div class="alert alert-warning">
                <i class="fas fa-warning"></i>
                <strong>Warning:</strong> This will send notifications every minute until the server is restarted. Use only for testing!
            </div>
            <button class="btn btn-test btn-danger-test" onclick="startPeriodicTest()">
                <i class="fas fa-play"></i> Start Periodic Sender
            </button>
        </div>

        <!-- Notification Preview -->
        <div class="test-section">
            <h3><i class="fas fa-eye"></i> Notification Preview</h3>
            <div class="notification-preview d-flex align-items-center">
                <div class="notification-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title" id="previewTitle">Test Notification #1</div>
                    <div class="notification-body" id="previewBody">This is test notification number 1 sent at 12:34:56</div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> Test Statistics</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalSent">0</div>
                    <div class="stat-label">Total Sent</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lastSent">Never</div>
                    <div class="stat-label">Last Sent</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="periodicStatus">Stopped</div>
                    <div class="stat-label">Periodic Status</div>
                </div>
            </div>
        </div>

        <!-- Status Log -->
        <div class="test-section">
            <h3><i class="fas fa-terminal"></i> Status Log</h3>
            <div class="status-log" id="statusLog">
                [SYSTEM] Notification tester initialized...<br>
                [INFO] Ready to send test notifications<br>
                [WARNING] This is a temporary testing function - delete after use!<br>
            </div>
        </div>

        <!-- Instructions -->
        <div class="test-section">
            <h3><i class="fas fa-info-circle"></i> Instructions</h3>
            <ol>
                <li>Make sure you have enabled notifications in your browser</li>
                <li>Use "Send Single Test" to test immediate notifications</li>
                <li>Use "Start Periodic Sender" to test continuous notifications (every minute)</li>
                <li>Check the browser's notification panel to see received notifications</li>
                <li><strong>IMPORTANT:</strong> Delete this function after testing!</li>
            </ol>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/pwa-manager.js' %}"></script>
    
    <script>
        let totalSent = 0;
        let periodicRunning = false;

        function addLog(message, type = 'INFO') {
            const log = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${type}] ${message}<br>`;
            log.innerHTML += logEntry;
            log.scrollTop = log.scrollHeight;
        }

        function updateStats() {
            document.getElementById('totalSent').textContent = totalSent;
            document.getElementById('lastSent').textContent = new Date().toLocaleTimeString();
            document.getElementById('periodicStatus').textContent = periodicRunning ? 'Running' : 'Stopped';
        }

        function updatePreview(counter = 1) {
            const currentTime = new Date().toLocaleTimeString();
            document.getElementById('previewTitle').textContent = `Test Notification #${counter}`;
            document.getElementById('previewBody').textContent = `This is test notification number ${counter} sent at ${currentTime}`;
        }

        function sendSingleTest() {
            addLog('Sending single test notification...', 'INFO');
            
            fetch('{% url "temp_notification_sender" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: 'action=test_single'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('Single test notification sent successfully!', 'SUCCESS');
                    totalSent++;
                    updateStats();
                    updatePreview(totalSent);
                } else {
                    addLog('Failed to send single test notification', 'ERROR');
                }
            })
            .catch(error => {
                addLog(`Error: ${error.message}`, 'ERROR');
            });
        }

        function startPeriodicTest() {
            if (periodicRunning) {
                addLog('Periodic sender is already running!', 'WARNING');
                return;
            }

            addLog('Starting periodic notification sender...', 'INFO');
            addLog('WARNING: This will send notifications every minute!', 'WARNING');
            
            fetch('{% url "temp_notification_sender" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: 'action=start'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addLog('Periodic notification sender started!', 'SUCCESS');
                    addLog(data.warning, 'WARNING');
                    periodicRunning = true;
                    updateStats();
                    
                    // Update UI to show it's running
                    const button = event.target;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running...';
                    button.disabled = true;
                } else {
                    addLog('Failed to start periodic sender', 'ERROR');
                }
            })
            .catch(error => {
                addLog(`Error: ${error.message}`, 'ERROR');
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Notification tester page loaded', 'INFO');
            updatePreview();
            
            // Check if PWA Manager is available
            if (window.pwaManager) {
                addLog('PWA Manager detected - notifications should work', 'SUCCESS');
            } else {
                addLog('PWA Manager not detected - notifications may not work', 'WARNING');
            }
            
            // Check notification permission
            if ('Notification' in window) {
                addLog(`Notification permission: ${Notification.permission}`, 'INFO');
                if (Notification.permission === 'default') {
                    addLog('You may need to enable notifications first', 'WARNING');
                }
            } else {
                addLog('Notifications not supported in this browser', 'ERROR');
            }
        });

        // Listen for service worker messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data.type === 'NOTIFICATION_RECEIVED') {
                    addLog('Notification received by service worker', 'SUCCESS');
                }
            });
        }
    </script>
</body>
</html>
